<?php

namespace app\admin\controller\system;

use app\common\controller\CommonController;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;

/**
 * @ControllerAnnotation(title="通道管理")
 */
class Passage extends CommonController
{
    public function __construct()
    {
        $authcode = get_setting('authcode');
        if(!$authcode){
            $this->error('请填写授权码');
        }
        $zz_id = '2';
        $zz_url = 'auth.zzwws.cn';
        $root = $_SERVER['DOCUMENT_ROOT'];
        $host = $_SERVER['HTTP_HOST'];
        $server_ip = !empty($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : $_SERVER['APP_POOL_ID'];
        $date = date("Y-m-d");
        $zz_auth = cache('zz_auth_'.$server_ip);
        if(md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM') !== $zz_auth){
        $zz_query=get_curl('http://'.$zz_url.'/ZR/api/zr_check.php?url='.$host.'&authcode='.$authcode.'&version=&ZRid='.$zz_id);
            if ($zz_query = json_decode($zz_query, true)) {
        		if ($zz_query["code"] == 0) {
        			cache('zz_auth_'.$server_ip,md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM'),3600*24);
        		}else{
        			$this->error($zz_query['msg']);
        		}
        	}else{
        	    $this->error('查询授权信息失败');
        	}
        }
        
        $this->model = new \app\admin\model\SystemPassage();
    }

    /**
	* @NodeAnotation(title="列表")
	*/
	public function index()
	{
		if(request()->isAjax()){
			$data = $this->model->index();
			return json([
				'code' => 0,
				'msg' => '',
				'count' => $data['count'],
				'data' => $data['data']
			]);
		}
		$url = parse_url(get_setting('epayurl_ali'));
		$host = $url['host'];
		if(!empty($url['port'])){
		    $host .= ':'.$url['port'];
		}
		$appConfig = $host.'/'.get_setting('epaykey_ali').'/';
		return view('',['payType' => $this->model->payType,'payCode' => $this->model->payCode,'appConfig' => $appConfig]);
	}

	/**
	* @NodeAnotation(title="添加")
	*/
	public function add()
	{
		if(request()->isAjax()){
			return $this->model->add();
		}
		return view('',['payType' => $this->model->payType,'payCode' => $this->model->payCode]);
	}

	/**
	* @NodeAnotation(title="编辑")
	*/
	public function edit()
	{
		if(request()->isAjax()){
			return $this->model->edit();
		}
		$row = $this->model->find(input('id'));
		if(!$row){
			$this->error('获取失败');
		}

		// 如果是支付宝通道，获取支付宝配置
		if ($row['type'] == 2) {
			$alipayConfig = \app\admin\model\AlipayConfig::getByPassageId($row['id']);
			if ($alipayConfig) {
				$row['alipay_config'] = $alipayConfig->toArray();
				// 为了兼容现有字段，也复制到原字段位置
				$row['transfer'] = $alipayConfig['transfer'];
				$row['avoid_ck'] = $alipayConfig['avoid_ck'];
				$row['app_id'] = $alipayConfig['app_id'];
				$row['private_key'] = $alipayConfig['private_key'];
				$row['public_key'] = $alipayConfig['public_key'];
			}
		}

		return view('',['row' => $row,'payType' => $this->model->payType,'payCode' => $this->model->payCode]);
	}

    /**
	* @NodeAnotation(title="删除")
	*/
	public function del()
	{
		if(request()->isAjax()){
			return $this->model->del();
		}
	}

	/**
	* @NodeAnotation(title="状态")
	*/
	public function status()
	{
		if(request()->isAjax()){
			return $this->model->status();
		}
	}

	/**
	 * @NodeAnotation(title="免挂登录与更新")
	 */
	public function hangFree()
	{
		if(request()->isAjax()){
			return $this->model->hangFree();
		}
	}

	/**
	 * @NodeAnotation(title="二维码解码")
	 */
	public function decodeQrcode()
	{
		if(request()->isAjax()){
			$url = input('url');
			if(!$url){
				return json(['code' => 0, 'msg' => 'URL不能为空']);
			}

			// 构建完整的图片URL
			$http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || 
						 (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
			$fullUrl = $http_type . $_SERVER['HTTP_HOST'] . $url;

			// 使用新的解码服务
			$result = \app\common\service\QrcodeService::decode($fullUrl);
			
			return json($result);
		}
	}

	/**
	 * @NodeAnotation(title="测试解码接口")
	 */
	public function testDecodeApi()
	{
		// if(request()->isAjax()){
					$testImageUrl = input('test_url', '');
		$result = \app\common\service\QrcodeService::testDecodeApi($testImageUrl);
			
			return json([
				'code' => $result['success'] ? 1 : 0,
				'msg' => $result['message'],
				'data' => $result
			]);
		// }
	}
}