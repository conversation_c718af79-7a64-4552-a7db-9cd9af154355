<?php

namespace app\admin\controller;

use app\common\controller\CommonController;
use think\App;
use think\facade\Cache;
use app\admin\model\SystemOrder;
use app\admin\model\SystemPassage;
use app\admin\model\SystemQuick;
use app\common\service\MenuService;
use app\common\service\UploadService;
use think\facade\Db;

class Index extends CommonController
{
    public function __construct()
    {
        $authcode = get_setting('authcode');
        if(!$authcode){
            $this->error('请填写授权码');
        }
        $zz_id = '2';
        $zz_url = 'auth.zzwws.cn';
        $root = $_SERVER['DOCUMENT_ROOT'];
        $host = $_SERVER['HTTP_HOST'];
        $server_ip = !empty($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : $_SERVER['APP_POOL_ID'];
        $date = date("Y-m-d");
        $zz_auth = cache('zz_auth_'.$server_ip);
		$version = str_replace(['v','V'],'',file_get_contents(ROOT_PATH.'version'));
        if(md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM') !== $zz_auth){
        $zz_query=get_curl('http://'.$zz_url.'/ZR/api/zr_check.php?url='.$host.'&authcode='.$authcode.'&version='.$version.'&ZRid='.$zz_id);
            if ($zz_query = json_decode($zz_query, true)) {
        		if ($zz_query["code"] == 0) {
        			cache('zz_auth_'.$server_ip,md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM'),3600*24);
        		}else{
        			$this->error($zz_query['msg']);
        		}
        	}else{
        	    $this->error('查询授权信息失败');
        	}
        }
    }
    
	public function getSystemInit()
	{
		return (new MenuService)->getSystemInit();
	}

	/**
	 * 后台首页
	 */
	public function index()
	{
		return view('',['id' => USER_INFO['id'],'username' => USER_INFO['username'],'head_img' => USER_INFO['head_img']]);
	}

	/**
	 * 后台欢迎页
	 */
	public function welcome()
	{
		$statistics = [
			['总收入',0,'blue'],
			['今日收入',0,'cyan'],
			['总订单',0,'orange'],
			['今日订单',0,'green']
		];

		// 快捷入口
		$quick = cache('zz_quick');
		if(!$quick){
			$quick = SystemQuick::field('href,title,icon')->where('status',1)->select()->toArray();
			cache('zz_quick',$quick);
		}

		// 系统公告
// 		$notice = cache('zz_notice');
// 		if(!$notice){
// 			$notice = SystemNotice::field('title,content,create_time')->where('status',1)->order('id','desc')->select()->toArray();
// 			cache('zz_notice',$notice);
// 		}
        $notice = [];

		$data = [
			'title' => get_setting('title'),
			'edition' => file_get_contents(ROOT_PATH.'version'),
			'statistics' => $statistics,
			'quick' => $quick,
			'notice' => $notice
		];

		return view('', $data);
	}
	
	/**
	 * 数据统计
	 */
	public function data()
	{
	    $time = strtotime(date('Y-m-d'));
		$income1 = SystemOrder::where('status','in',[1,2])->sum('truemoney');
        $income2 = SystemOrder::where([['status','in',[1,2]],['create_time','>=',$time]])->sum('truemoney');
        $order1 = SystemOrder::count('truemoney');
        $order2 = SystemOrder::where('create_time','>=',$time)->count('truemoney');

		// 获取昨日数据用于计算增长率
		$yesterdayStart = strtotime(date('Y-m-d', strtotime('-1 day')));
		$yesterdayEnd = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));
		$yesterdayIncome = SystemOrder::where([['status','in',[1,2]],['create_time','>=',$yesterdayStart],['create_time','<=',$yesterdayEnd]])->sum('truemoney');
		$yesterdayOrders = SystemOrder::where([['create_time','>=',$yesterdayStart],['create_time','<=',$yesterdayEnd]])->count('truemoney');

		// 计算增长率
		$incomeGrowth = $yesterdayIncome > 0 ? round(($income2 - $yesterdayIncome) / $yesterdayIncome * 100, 2) : 0;
		$orderGrowth = $yesterdayOrders > 0 ? round(($order2 - $yesterdayOrders) / $yesterdayOrders * 100, 2) : 0;

		// 获取通道数据
		$activeChannels = SystemPassage::where('status', 1)->count();
		$onlineChannels = SystemPassage::where([['status', '=', 1], ['is_status', '=', 1]])->count();

		// 获取支付方式统计
		// 支付模式名称映射
		$payTypeMap = [
			1 => '微信支付',
			2 => '支付宝',
			3 => 'QQ支付',
			4 => '数字人民币',
		];
		
		// 首先从订单表中获取每个支付模式的统计数据
		$payTypeData = SystemOrder::field('type, count(*) as count, sum(truemoney) as amount')
			->where([['status','in',[1,2]],['create_time','>=',$time]])
			->group('type')
			->select()
			->toArray();
		
		// 获取通道管理表中的支付方式
		$passages = SystemPassage::field('id, remarks, type')->where('status', 1)->select()->toArray();
		
		// 按支付模式（type）分组的支付方式统计
		$payTypeStats = [];
		
		// 确保所有支付方式都有数据，即使今日没有交易
		$allPayTypes = [1, 2, 3, 4];
		
		foreach ($allPayTypes as $type) {
			// 初始化支付方式数据
			$payTypeStats[$type] = [
				'type' => $type,
				'type_name' => $payTypeMap[$type] ?? '未知',
				'count' => 0,
				'amount' => 0,
				'channels' => []
			];
			
			// 从订单数据中获取该支付方式的统计
			foreach ($payTypeData as $data) {
				if ($data['type'] == $type) {
					$payTypeStats[$type]['count'] = $data['count'] ?: 0;
					$payTypeStats[$type]['amount'] = $data['amount'] ?: 0;
					break;
				}
			}
			
			// 获取该支付模式下的所有通道
			foreach ($passages as $passage) {
				if ($passage['type'] == $type) {
					// 获取该通道的统计数据
					$channelStats = SystemOrder::field('count(*) as count, sum(truemoney) as amount')
						->where([['status','in',[1,2]],['create_time','>=',$time],['pid','=',$passage['id']]])
						->find();
					
					$payTypeStats[$type]['channels'][] = [
						'id' => $passage['id'],
						'title' => $passage['remarks'] ?: '通道' . $passage['id'],
						'count' => $channelStats['count'] ?: 0,
						'amount' => $channelStats['amount'] ?: 0
					];
				}
			}
		}
		
		// 将分组数据转换为数组
		$payTypeStats = array_values($payTypeStats);

		// 获取每小时统计（今日24小时）
		$hourlyStats = [];
		for ($i = 0; $i < 24; $i++) {
			$hourStart = strtotime(date('Y-m-d ') . sprintf('%02d:00:00', $i));
			$hourEnd = strtotime(date('Y-m-d ') . sprintf('%02d:59:59', $i));
			
			$hourlyStats[] = [
				'hour' => sprintf('%02d:00', $i),
				'orders' => SystemOrder::where([['create_time','>=',$hourStart],['create_time','<=',$hourEnd]])->count(),
				'amount' => SystemOrder::where([['status','in',[1,2]],['create_time','>=',$hourStart],['create_time','<=',$hourEnd]])->sum('truemoney')
			];
		}

		// 获取7天图表数据
		$chartData = [
			'days' => [],
			'income' => [],
			'orders' => []
		];
		
		for ($i = 6; $i >= 0; $i--) {
			$dayStart = strtotime(date('Y-m-d', strtotime("-$i day")));
			$dayEnd = strtotime(date('Y-m-d 23:59:59', strtotime("-$i day")));
			
			// 每日收入
			$dailyIncome = SystemOrder::where([['status','in',[1,2]],['create_time','>=',$dayStart],['create_time','<=',$dayEnd]])->sum('truemoney') ?: 0;
			
			// 每日订单
			$dailyOrders = SystemOrder::where([['create_time','>=',$dayStart],['create_time','<=',$dayEnd]])->count('id') ?: 0;
			
			$chartData['days'][] = date('m/d', strtotime("-$i day"));
			$chartData['income'][] = $dailyIncome;
			$chartData['orders'][] = $dailyOrders;
		}

		// 获取成功率
		$totalOrdersToday = SystemOrder::where('create_time', '>=', $time)->count();
		$successOrdersToday = SystemOrder::where([['status', '=', 1], ['create_time', '>=', $time]])->count();
		$failedOrdersToday = SystemOrder::where([['status', '=', 3], ['create_time', '>=', $time]])->count();
		$successRate = $totalOrdersToday > 0 ? round($successOrdersToday / $totalOrdersToday * 100, 2) : 0;

		// 获取今日最大订单金额
		$maxOrderToday = SystemOrder::where([['status', 'in', [1,2]], ['create_time', '>=', $time]])->max('truemoney') ?: 0;

		// 获取今日订单峰值时段
		$peakHour = 0;
		$maxHourlyOrders = 0;
		foreach ($hourlyStats as $hourData) {
			if ($hourData['orders'] > $maxHourlyOrders) {
				$maxHourlyOrders = $hourData['orders'];
				$peakHour = intval($hourData['hour']);
			}
		}

		// 获取平均处理时间（秒）
		$avgProcessTime = 0;
		$processedOrders = SystemOrder::field('pay_time, create_time')
			->where([['status', '=', 1], ['create_time', '>=', $time], ['pay_time', '>', 0]])
			->select()
			->toArray();
		
		if (!empty($processedOrders)) {
			$totalTime = 0;
			foreach ($processedOrders as $order) {
				$totalTime += ($order['pay_time'] - $order['create_time']);
			}
			$avgProcessTime = round($totalTime / count($processedOrders), 1);
		}

		// 支付方式排行（按金额排序）
		$payTypeRanking = [];
		$totalDayAmount = array_sum(array_column($payTypeStats, 'amount'));
		usort($payTypeStats, function($a, $b) {
			return $b['amount'] <=> $a['amount'];
		});

		foreach (array_slice($payTypeStats, 0, 3) as $index => $stat) {
			$percent = $totalDayAmount > 0 ? round($stat['amount'] / $totalDayAmount * 100, 1) : 0;
			$payTypeRanking[] = [
				'rank' => $index + 1,
				'name' => $stat['type_name'],
				'amount' => $stat['amount'],
				'percent' => $percent
			];
		}

		// 补齐排行榜到3个
		while (count($payTypeRanking) < 3) {
			$payTypeRanking[] = [
				'rank' => count($payTypeRanking) + 1,
				'name' => '暂无数据',
				'amount' => 0,
				'percent' => 0
			];
		}

		$data = [
			'income1' => $income1,
			'income2' => $income2,
			'order1' => $order1,
			'order2' => $order2,
			'incomeGrowth' => $incomeGrowth,
			'orderGrowth' => $orderGrowth,
			'activeChannels' => $activeChannels,
			'onlineChannels' => $onlineChannels,
			'successRate' => $successRate,
			'failedOrdersToday' => $failedOrdersToday,
			'maxOrderToday' => $maxOrderToday,
			'peakHour' => $peakHour,
			'avgProcessTime' => $avgProcessTime,
			'payTypeRanking' => $payTypeRanking,
			'chartData' => $chartData,
			'payTypeStats' => $payTypeStats,
			'hourlyStats' => $hourlyStats
		];
		return json(['code' => 0, 'msg' => '获取成功', 'data' => $data]);
	}
	
	/**
	 * 检查更新，每小时一次
	 */ 
	public function checkUpdate(){
        $checkUpdate = cache('zz_check_update');
        if(!$checkUpdate){
            cache('zz_check_update',time(),3600);
            $ver = get_curl("https://www.zzwws.cn/api.php?q=V免签易支付版");
            $arr = json_decode($ver,true);
            if ($arr['version'] > str_replace(['v','V'],'',file_get_contents(ROOT_PATH.'version'))){
                $this->success("[v".$arr['version']."已于".$arr['updateTime']."发布]","{$arr['url']}");
            }else{
                $this->error('程序是最新版');
            }
        }else{
            $this->error('已经提示过了');
        }
    }
    
    /**
     * 更新程序
     */ 
    public function update()
    {
        if(stripos($_SERVER['HTTP_HOST'],'pay.zzwws') !== false){
            $this->error('指定站点无需在线更新');// 更新包有加密，不能直接替换
        }
        $ver = get_curl("https://www.zzwws.cn/api.php?q=V免签易支付版");
        $arr = json_decode($ver,true);
        $version = str_replace(['v','V'],'',file_get_contents(ROOT_PATH.'version'));
        // halt($arr);
        if ($arr['version'] > $version){
            $content = get_curl($arr['updateUrl'],'','','https://www.zzwws.cn/','','','',[],0);
            if(!$content){
                $this->error('获取更新包失败');
            }
            $dir = public_path().'update'.DS;
            $root = root_path();
            if(!is_dir($dir)){
                mkdir($dir,0777,true);
            }
            file_put_contents($dir.$arr['version'].'.zip',$content);
            $zip = new \ZipArchive();
            if($zip->open($dir.$arr['version'].'.zip') === true){
                if ($zip->locateName('version') !== false) {
                    $version = $zip->getFromName('version');
                    $zip->deleteName('version');
                }
                $zip->extractTo($root);
                if(is_file($root.'update.sql')){
                    $sql = file_get_contents($root.'update.sql');
                    $sql = str_replace('zz_',env('database.prefix', 'zz_'),$sql);
                    $sqlArray = explode("\n", trim(str_replace(["\r\n", "\r"], "\n", $sql)));
                    $update_sql = false;
        
                    foreach ($sqlArray as $v){
                        if(strpos($v,'/*') !== false){
                            $sql_version = str_replace(['/*','*/',' '],'',$v);
                            if($sql_version > $version){
                                $update_sql = true;
                            }
                        } 
                        if($v && strpos($v,'/*') === false && $update_sql){
                            Db::query($v);// SQL语句不能重复执行，有些会报错
                        }
                    }

					if($update_sql){
						Cache::clear();
					}
                }
                do_rmdir($dir);
                if(!empty($version)){
                    file_put_contents($root.'version',$version);
                }
                $this->success('更新成功');
            }else{
                $this->error('打开更新包失败或不存在');
            }
        }else{
            $this->error('程序是最新版');
        }
    }

	/**
	 * 清空缓存
	 */
	public function clear()
	{
		if (request()->isAjax()) {
			if(USER_INFO['role'] != 1){
				$this->error('你没有权限访问');
			}
			Cache::clear();
			do_rmdir(ROOT_PATH.'public'.DS.'ck',false);
			$this->success('缓存已清空');
		}
	}

	public function cs()
	{

	}

	/**
	 * 图片上传
	 */
	public function upload()
	{
		$type = input('type');
		$folder = 'common';
		$filename = '';
		if($type === 'logo_image'){
			$folder = '';
			$filename = input('filename') ?: get_setting('logo_image');
		}else if($type === 'favicon'){
			$folder = '';
			$filename = input('filename') ?: get_setting('favicon');
		}
		return (new UploadService)->upload($folder,$filename);
	}

	/**
     * 获取数据库版本
     * @return string
     */
    protected function mysqlVersion()
    {
        $mysql = cache('zz_version_'.$_SERVER['SERVER_ADDR']);
        if(!$mysql){
            $mysql = Db::query('select version() as version');
            $mysql = $mysql[0]['version'];
            $mysql = empty($mysql) ? '未知' : $mysql;
            cache('zz_version_'.$_SERVER['SERVER_ADDR'],$mysql);
        }
        return $mysql;
    }
    
    /**
     * 获取最近7天的时间
	 * @param int $date 指定时间，时间戳
     */
    protected function getDay($date = 0)
    {
		if($date){
			$time = $date;
		}else{
			$time = time();
		}
        $arr = [];
        $week_day_num = date('w', $time);
        // 当前是周日的情况
        if ($week_day_num == 0 || $date) {
            $sdate = strtotime("-6 day", $time);
        } else {
            $sdate = strtotime("-" . ($week_day_num - 1) . " day", $time);
        }
        for($i = -1;$i < 6;$i++){
            $arr[] = date('Y-m-d',strtotime("+".($i+1)." day", $sdate));
        }
        return $arr;
    }

    /**
     * 导出财务报表
     */
    public function exportReport()
    {
        // 获取报表数据
        $time = strtotime(date('Y-m-d'));
        $income1 = SystemOrder::where('status','in',[1,2])->sum('truemoney');
        $income2 = SystemOrder::where([['status','in',[1,2]],['create_time','>=',$time]])->sum('truemoney');
        $order1 = SystemOrder::count('truemoney');
        $order2 = SystemOrder::where('create_time','>=',$time)->count('truemoney');

        // 获取通道数据
        $activeChannels = SystemPassage::where('status', 1)->count();
        $onlineChannels = SystemPassage::where([['status', '=', 1], ['is_status', '=', 1]])->count();

        // 获取成功率
        $totalOrdersToday = SystemOrder::where('create_time', '>=', $time)->count();
        $successOrdersToday = SystemOrder::where([['status', '=', 1], ['create_time', '>=', $time]])->count();
        $failedOrdersToday = SystemOrder::where([['status', '=', 3], ['create_time', '>=', $time]])->count();
        $successRate = $totalOrdersToday > 0 ? round($successOrdersToday / $totalOrdersToday * 100, 2) : 0;

        // 支付模式名称映射
        $payTypeMap = [
            1 => '微信支付',
            2 => '支付宝',
            3 => 'QQ支付',
            4 => '数字人民币',
        ];
        
        // 获取支付方式统计
        $payTypeData = SystemOrder::field('type, count(*) as count, sum(truemoney) as amount')
            ->where([['status','in',[1,2]],['create_time','>=',$time]])
            ->group('type')
            ->select()
            ->toArray();
        
        // 获取通道管理表中的支付方式
        $passages = SystemPassage::field('id, remarks, type')->where('status', 1)->select()->toArray();
        
        // 按支付模式（type）分组的支付方式统计
        $payTypeStats = [];
        
        // 确保所有支付方式都有数据，即使今日没有交易
        $allPayTypes = [1, 2, 3, 4];
        
        foreach ($allPayTypes as $type) {
            // 初始化支付方式数据
            $payTypeStats[$type] = [
                'type' => $type,
                'type_name' => $payTypeMap[$type] ?? '未知',
                'count' => 0,
                'amount' => 0,
                'channels' => []
            ];
            
            // 从订单数据中获取该支付方式的统计
            foreach ($payTypeData as $data) {
                if ($data['type'] == $type) {
                    $payTypeStats[$type]['count'] = $data['count'] ?: 0;
                    $payTypeStats[$type]['amount'] = $data['amount'] ?: 0;
                    break;
                }
            }
            
            // 获取该支付模式下的所有通道
            foreach ($passages as $passage) {
                if ($passage['type'] == $type) {
                    // 获取该通道的统计数据
                    $channelStats = SystemOrder::field('count(*) as count, sum(truemoney) as amount')
                        ->where([['status','in',[1,2]],['create_time','>=',$time],['pid','=',$passage['id']]])
                        ->find();
                    
                    $payTypeStats[$type]['channels'][] = [
                        'id' => $passage['id'],
                        'title' => $passage['remarks'] ?: '通道' . $passage['id'],
                        'count' => $channelStats['count'] ?: 0,
                        'amount' => $channelStats['amount'] ?: 0
                    ];
                }
            }
        }
        
        // 将分组数据转换为数组
        $payTypeStats = array_values($payTypeStats);

        // 获取7天图表数据
        $chartData = [
            'days' => [],
            'income' => [],
            'orders' => []
        ];
        
        for ($i = 6; $i >= 0; $i--) {
            $dayStart = strtotime(date('Y-m-d', strtotime("-$i day")));
            $dayEnd = strtotime(date('Y-m-d 23:59:59', strtotime("-$i day")));
            
            // 每日收入
            $dailyIncome = SystemOrder::where([['status','in',[1,2]],['create_time','>=',$dayStart],['create_time','<=',$dayEnd]])->sum('truemoney') ?: 0;
            
            // 每日订单
            $dailyOrders = SystemOrder::where([['create_time','>=',$dayStart],['create_time','<=',$dayEnd]])->count('id') ?: 0;
            
            $chartData['days'][] = date('m/d', strtotime("-$i day"));
            $chartData['income'][] = $dailyIncome;
            $chartData['orders'][] = $dailyOrders;
        }

        // 获取24小时统计数据
        $hourlyStats = [];
        for ($i = 0; $i < 24; $i++) {
            $hourStart = strtotime(date('Y-m-d ') . sprintf('%02d:00:00', $i));
            $hourEnd = strtotime(date('Y-m-d ') . sprintf('%02d:59:59', $i));
            
            $hourlyStats[] = [
                'hour' => sprintf('%02d:00', $i),
                'orders' => SystemOrder::where([['create_time','>=',$hourStart],['create_time','<=',$hourEnd]])->count(),
                'amount' => SystemOrder::where([['status','in',[1,2]],['create_time','>=',$hourStart],['create_time','<=',$hourEnd]])->sum('truemoney')
            ];
        }

        // 获取今日最大订单金额
        $maxOrderToday = SystemOrder::where([['status', 'in', [1,2]], ['create_time', '>=', $time]])->max('truemoney') ?: 0;

        // 获取今日订单峰值时段
        $peakHour = 0;
        $maxHourlyOrders = 0;
        foreach ($hourlyStats as $hourData) {
            if ($hourData['orders'] > $maxHourlyOrders) {
                $maxHourlyOrders = $hourData['orders'];
                $peakHour = intval($hourData['hour']);
            }
        }

        // 获取平均处理时间（秒）
        $avgProcessTime = 0;
        $processedOrders = SystemOrder::field('pay_time, create_time')
            ->where([['status', '=', 1], ['create_time', '>=', $time], ['pay_time', '>', 0]])
            ->select()
            ->toArray();
        
        if (!empty($processedOrders)) {
            $totalTime = 0;
            foreach ($processedOrders as $order) {
                $totalTime += ($order['pay_time'] - $order['create_time']);
            }
            $avgProcessTime = round($totalTime / count($processedOrders), 1);
        }

        // 设置CSV文件名
        $filename = '财务报表_' . date('Y-m-d') . '.csv';
        
        // 设置HTTP头
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);
        
        // 打开输出流
        $output = fopen('php://output', 'w');
        
        // 添加BOM头，确保Excel正确显示中文
        fwrite($output, "\xEF\xBB\xBF");
        
        // 写入报表标题
        fputcsv($output, ['财务报表分析']);
        fputcsv($output, ['生成时间: ' . date('Y-m-d H:i:s')]);
        fputcsv($output, []);
        
        // 写入总体统计
        fputcsv($output, ['总体统计']);
        fputcsv($output, ['总收入', '今日收入', '总订单', '今日订单']);
        fputcsv($output, ['¥' . number_format($income1, 2), '¥' . number_format($income2, 2), $order1, $order2]);
        fputcsv($output, []);
        
        // 写入通道状态统计
        fputcsv($output, ['通道状态统计']);
        fputcsv($output, ['在线通道', '总通道数', '今日成功率', '失败订单数']);
        fputcsv($output, [$onlineChannels, $activeChannels, $successRate . '%', $failedOrdersToday]);
        fputcsv($output, []);
        
        // 写入今日数据概览
        fputcsv($output, ['今日数据概览']);
        fputcsv($output, ['订单峰值时段', '最大单笔金额', '平均处理时间']);
        fputcsv($output, [$peakHour . ':00', '¥' . number_format($maxOrderToday, 2), $avgProcessTime . 's']);
        fputcsv($output, []);
        
        // 写入支付方式分布
        fputcsv($output, ['支付方式分布']);
        fputcsv($output, ['支付方式', '订单数', '金额', '占比']);
        
        $totalDayAmount = array_sum(array_column($payTypeStats, 'amount'));
        foreach ($payTypeStats as $stat) {
            $percent = $totalDayAmount > 0 ? round($stat['amount'] / $totalDayAmount * 100, 1) : 0;
            fputcsv($output, [
                $stat['type_name'],
                $stat['count'],
                '¥' . number_format($stat['amount'], 2),
                $percent . '%'
            ]);
        }
        fputcsv($output, []);
        
        // 写入通道详情
        fputcsv($output, ['通道详情']);
        fputcsv($output, ['支付方式', '通道名称', '订单数', '金额']);
        
        foreach ($payTypeStats as $payType) {
            if (!empty($payType['channels'])) {
                foreach ($payType['channels'] as $channel) {
                    fputcsv($output, [
                        $payType['type_name'],
                        $channel['title'],
                        $channel['count'],
                        '¥' . number_format($channel['amount'], 2)
                    ]);
                }
            }
        }
        fputcsv($output, []);
        
        // 写入7日趋势数据
        fputcsv($output, ['7日趋势数据']);
        fputcsv($output, ['日期', '收入', '订单数']);
        
        for ($i = 0; $i < count($chartData['days']); $i++) {
            fputcsv($output, [
                $chartData['days'][$i],
                '¥' . number_format($chartData['income'][$i], 2),
                $chartData['orders'][$i]
            ]);
        }
        fputcsv($output, []);
        
        // 写入24小时统计数据
        fputcsv($output, ['24小时统计数据']);
        fputcsv($output, ['时间', '订单数', '金额']);
        
        foreach ($hourlyStats as $hourData) {
            fputcsv($output, [
                $hourData['hour'],
                $hourData['orders'],
                '¥' . number_format($hourData['amount'], 2)
            ]);
        }
        
        // 关闭输出流
        fclose($output);
        
        // 终止脚本执行
        exit;
    }
}
