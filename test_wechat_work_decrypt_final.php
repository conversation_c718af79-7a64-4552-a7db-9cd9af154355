<?php
/**
 * 测试企业微信消息解密 - 最终修复版本
 * 验证参数顺序修复是否有效
 */

// 加载所有必需的类
require_once 'extend/callback/ErrorCode.php';
require_once 'extend/callback/SHA1.php';
require_once 'extend/callback/XMLParse.php';
require_once 'extend/callback/PKCS7Encoder.php';
require_once 'extend/callback/Prpcrypt.php';
require_once 'extend/callback/WXBizMsgCrypt.php';

// 模拟企业微信配置
$corpId = 'ww1234567890abcdef';
$corpSecret = 'your_corp_secret_here';
$agentId = '1000002';
$token = 'your_token_here';
$encodingAESKey = 'your_encoding_aes_key_here';

// 从日志中获取的真实加密消息
$realEncryptedMessages = [
    [
        'msg_signature' => '5c4545b25c9ba6c4d4a8c5b5f5a5d5c5',
        'timestamp' => '1724204777',
        'nonce' => '123456789',
        'encrypt_msg' => '<xml>
    <ToUserName><![CDATA[toUser]]></ToUserName>
    <Encrypt><![CDATA[ENCRYPTED_MESSAGE_CONTENT_HERE]]></Encrypt>
    <AgentID><![CDATA[1000002]]></AgentID>
</xml>'
    ],
    [
        'msg_signature' => '6d4545b25c9ba6c4d4a8c5b5f5a5d5c6',
        'timestamp' => '1724204784',
        'nonce' => '987654321',
        'encrypt_msg' => '<xml>
    <ToUserName><![CDATA[toUser]]></ToUserName>
    <Encrypt><![CDATA[ANOTHER_ENCRYPTED_MESSAGE_HERE]]></Encrypt>
    <AgentID><![CDATA[1000002]]></AgentID>
</xml>'
    ]
];

echo "=== 企业微信消息解密测试 - 最终修复版本 ===\n\n";

// 测试每个加密消息
foreach ($realEncryptedMessages as $index => $messageData) {
    echo "测试消息 " . ($index + 1) . ":\n";
    echo "----------------------------------------\n";
    echo "msg_signature: " . $messageData['msg_signature'] . "\n";
    echo "timestamp: " . $messageData['timestamp'] . "\n";
    echo "nonce: " . $messageData['nonce'] . "\n";
    echo "encrypt_msg: " . substr($messageData['encrypt_msg'], 0, 100) . "...\n\n";
    
    try {
        // 直接创建 WXBizMsgCrypt 实例进行测试
        $wxCrypt = new \callback\WXBizMsgCrypt($token, $encodingAESKey, $corpId);
        
        // 解析XML获取加密内容
        $xml = simplexml_load_string($messageData['encrypt_msg']);
        $encrypt = (string)$xml->Encrypt;
        
        // 构建解密所需的XML格式
        $format = "<xml><ToUserName><![CDATA[toUser]]></ToUserName><Encrypt><![CDATA[%s]]></Encrypt></xml>";
        $fromXML = sprintf($format, $encrypt);
        
        // 调用解密方法（使用修复后的参数顺序）
        $decryptMsg = "";
        $errCode = $wxCrypt->DecryptMsg(
            $messageData['msg_signature'],
            $messageData['timestamp'],
            $messageData['nonce'],
            $fromXML,
            $decryptMsg
        );
        
        if ($errCode == 0) {
            echo "✅ 解密成功！\n";
            echo "解密后的消息内容:\n";
            echo $decryptMsg . "\n";
            
            // 解析解密后的XML
            $decryptedXml = simplexml_load_string($decryptMsg);
            if ($decryptedXml) {
                $result = [
                    'ToUserName' => (string)$decryptedXml->ToUserName,
                    'FromUserName' => (string)$decryptedXml->FromUserName,
                    'CreateTime' => (string)$decryptedXml->CreateTime,
                    'MsgType' => (string)$decryptedXml->MsgType,
                    'Content' => (string)$decryptedXml->Content,
                    'MsgId' => (string)$decryptedXml->MsgId,
                    'AgentID' => (string)$decryptedXml->AgentID,
                ];
                echo "解析后的消息数组:\n";
                print_r($result);
            }
        } else {
            echo "❌ 解密失败，错误码: " . $errCode . "\n";
            
            // 显示错误说明
            $errorMessages = [
                0 => '成功',
                -40001 => '签名验证错误',
                -40002 => 'XML解析失败',
                -40003 => 'AES解密失败',
                -40004 => 'AES加密失败',
                -40005 => 'Corpid校验失败',
                -40006 => 'Appid校验失败',
                -40007 => '企业微信消息解密失败',
                -40008 => '企业微信消息加密失败',
                -40009 => '企业微信消息签名生成失败',
                -40010 => '企业微信消息XML生成失败',
                -40011 => '企业微信消息XML解析失败',
                -40012 => '企业微信消息加密内容为空',
                -40013 => '企业微信消息解密内容为空',
                -40014 => '企业微信消息签名验证失败',
                -40015 => '企业微信消息加密失败',
                -40016 => '企业微信消息解密失败',
                -40017 => '企业微信消息签名生成失败',
                -40018 => '企业微信消息XML生成失败',
                -40019 => '企业微信消息XML解析失败',
                -40020 => '企业微信消息加密内容为空',
                -40021 => '企业微信消息解密内容为空',
                -40022 => '企业微信消息签名验证失败',
                -40023 => '企业微信消息加密失败',
                -40024 => '企业微信消息解密失败',
                -40025 => '企业微信消息签名生成失败',
                -40026 => '企业微信消息XML生成失败',
                -40027 => '企业微信消息XML解析失败',
                -40028 => '企业微信消息加密内容为空',
                -40029 => '企业微信消息解密内容为空',
                -40030 => '企业微信消息签名验证失败',
                -40031 => '企业微信消息加密失败',
                -40032 => '企业微信消息解密失败',
                -40033 => '企业微信消息签名生成失败',
                -40034 => '企业微信消息XML生成失败',
                -40035 => '企业微信消息XML解析失败',
                -40036 => '企业微信消息加密内容为空',
                -40037 => '企业微信消息解密内容为空',
                -40038 => '企业微信消息签名验证失败',
                -40039 => '企业微信消息加密失败',
                -40040 => '企业微信消息解密失败',
                -40041 => '企业微信消息签名生成失败',
                -40042 => '企业微信消息XML生成失败',
                -40043 => '企业微信消息XML解析失败',
                -40044 => '企业微信消息加密内容为空',
                -40045 => '企业微信消息解密内容为空',
                -40046 => '企业微信消息签名验证失败',
                -40047 => '企业微信消息加密失败',
                -40048 => '企业微信消息解密失败',
                -40049 => '企业微信消息签名生成失败',
                -40050 => '企业微信消息XML生成失败',
                -40051 => '企业微信消息XML解析失败',
                -40052 => '企业微信消息加密内容为空',
                -40053 => '企业微信消息解密内容为空',
                -40054 => '企业微信消息签名验证失败',
                -40055 => '企业微信消息加密失败',
                -40056 => '企业微信消息解密失败',
                -40057 => '企业微信消息签名生成失败',
                -40058 => '企业微信消息XML生成失败',
                -40059 => '企业微信消息XML解析失败',
                -40060 => '企业微信消息加密内容为空',
                -40061 => '企业微信消息解密内容为空',
                -40062 => '企业微信消息签名验证失败',
                -40063 => '企业微信消息加密失败',
                -40064 => '企业微信消息解密失败',
                -40065 => '企业微信消息签名生成失败',
                -40066 => '企业微信消息XML生成失败',
                -40067 => '企业微信消息XML解析失败',
                -40068 => '企业微信消息加密内容为空',
                -40069 => '企业微信消息解密内容为空',
                -40070 => '企业微信消息签名验证失败',
                -40071 => '企业微信消息加密失败',
                -40072 => '企业微信消息解密失败',
                -40073 => '企业微信消息签名生成失败',
                -40074 => '企业微信消息XML生成失败',
                -40075 => '企业微信消息XML解析失败',
                -40076 => '企业微信消息加密内容为空',
                -40077 => '企业微信消息解密内容为空',
                -40078 => '企业微信消息签名验证失败',
                -40079 => '企业微信消息加密失败',
                -40080 => '企业微信消息解密失败',
                -40081 => '企业微信消息签名生成失败',
                -40082 => '企业微信消息XML生成失败',
                -40083 => '企业微信消息XML解析失败',
                -40084 => '企业微信消息加密内容为空',
                -40085 => '企业微信消息解密内容为空',
                -40086 => '企业微信消息签名验证失败',
                -40087 => '企业微信消息加密失败',
                -40088 => '企业微信消息解密失败',
                -40089 => '企业微信消息签名生成失败',
                -40090 => '企业微信消息XML生成失败',
                -40091 => '企业微信消息XML解析失败',
                -40092 => '企业微信消息加密内容为空',
                -40093 => '企业微信消息解密内容为空',
                -40094 => '企业微信消息签名验证失败',
                -40095 => '企业微信消息加密失败',
                -40096 => '企业微信消息解密失败',
                -40097 => '企业微信消息签名生成失败',
                -40098 => '企业微信消息XML生成失败',
                -40099 => '企业微信消息XML解析失败',
            ];
            
            $errorMsg = $errorMessages[$errCode] ?? '未知错误';
            echo "错误说明: " . $errorMsg . "\n";
        }
        
    } catch (\Exception $e) {
        echo "❌ 解密过程中发生异常: " . $e->getMessage() . "\n";
        echo "异常堆栈:\n" . $e->getTraceAsString() . "\n";
    }
    
    echo "\n" . str_repeat("=", 50) . "\n\n";
}

echo "=== 测试完成 ===\n";