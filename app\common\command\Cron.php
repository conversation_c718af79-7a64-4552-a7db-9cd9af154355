<?php

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\console\input\Argument;
use app\job\CronJob;

class Cron extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('cron')->setDescription('定时任务队列推送（每分钟执行一次）');
        // 设置参数
        // ->addArgument('status', Argument::REQUIRED, 'start/stop/reload/status/connections')
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('[' . date('Y-m-d H:i:s') . '] 开始执行定时任务推送...');
        
        try {
            $alipay = \app\admin\model\SystemPassage::where('type',2)->where('code',9)->where('hang_free',1)->count();
		    $qqpay = \app\admin\model\SystemPassage::where('type',3)->where('hang_free',1)->count();
            
            // 推送主定时任务（每分钟执行一次）
            \think\facade\Queue::push(CronJob::class . '@mainCron', []);// , 'main_cron_' . time()
            // $output->writeln('[' . date('Y-m-d H:i:s') . '] 主定时任务已推送到队列');
            
            // 推送回调监控任务（每分钟推送60次，相当于每秒执行一次）
            for ($i = 0; $i < 60; $i++) {
                // 支付宝监控任务
                if($alipay){
                    \think\facade\Queue::push(CronJob::class . '@aliMonitor', []);// , 'ali_monitor_' . time() . '_' . $i   
                }
                
                // QQ监控任务  
                if($qqpay){
                    \think\facade\Queue::push(CronJob::class . '@qqMonitor', []);// , 'qq_monitor_' . time() . '_' . $i   
                }
                
                // 每秒推送一次，总共60次
                if ($i < 59) {
                    sleep(1);
                }
            }
            
            // $output->writeln('[' . date('Y-m-d H:i:s') . '] 回调监控任务已推送到队列（60次）');
            $output->writeln('[' . date('Y-m-d H:i:s') . '] 定时任务推送完成');
            
        } catch (\Exception $e) {
            $output->writeln('[' . date('Y-m-d H:i:s') . '] 定时任务推送失败: ' . $e->getMessage());
            exit(1); // 返回错误状态码
        }
    }
}
