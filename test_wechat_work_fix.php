<?php
require_once 'app/common.php';
require_once 'extend/callback/WXBizMsgCrypt.php';

// 测试企业微信消息解密
$corpId = get_setting('wechat_work_corp_id');
$token = get_setting('wechat_work_token');
$encodingAESKey = get_setting('wechat_work_encoding_aes_key');

echo '企业微信配置:' . PHP_EOL;
echo 'CorpId: ' . $corpId . PHP_EOL;
echo 'Token: ' . $token . PHP_EOL;
echo 'EncodingAESKey: ' . $encodingAESKey . PHP_EOL;

if (empty($corpId) || empty($token) || empty($encodingAESKey)) {
    echo '错误: 企业微信配置不完整' . PHP_EOL;
    exit(1);
}

// 创建 WXBizMsgCrypt 实例
$wxCrypt = new \callback\WXBizMsgCrypt($token, $encodingAESKey, $corpId);

// 测试数据
$msgSignature = 'test_signature';
$timestamp = '1234567890';
$nonce = 'test_nonce';
$encryptMsg = 'test_encrypt_msg';

// 测试解密
$decryptMsg = '';
$errCode = $wxCrypt->DecryptMsg($msgSignature, $timestamp, $nonce, $encryptMsg, $decryptMsg);

echo '解密结果:' . PHP_EOL;
echo '错误码: ' . $errCode . PHP_EOL;
echo '解密消息: ' . $decryptMsg . PHP_EOL;