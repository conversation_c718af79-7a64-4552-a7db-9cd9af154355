<?php

namespace app\index\controller;

use app\admin\model\AlipayConfig;
use app\admin\model\SystemOrder;
use app\admin\model\SystemPassage;
use app\admin\model\SystemQrcode;
use app\admin\model\TmpPrice;
use app\admin\model\WxpayConfig;
use app\common\controller\CommonController;
use app\common\service\AliPayService;
use app\common\service\EPayService;
use app\common\service\WeChatPayNativeV3Service;
use think\facade\Db;
use think\facade\Queue;

class Pay extends CommonController
{
    public function __construct()
    {
        header('Access-Control-Allow-Origin: *');
        
        $authcode = get_setting('authcode');
        if(!$authcode){
            $this->error('请填写授权码');
        }
        $zz_id = '2';
        $zz_url = 'auth.zzwws.cn';
        $root = $_SERVER['DOCUMENT_ROOT'];
        $host = $_SERVER['HTTP_HOST'];
        $server_ip = !empty($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : $_SERVER['APP_POOL_ID'];
        $date = date("Y-m-d");
        $zz_auth = cache('zz_auth_'.$server_ip);
        if(md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM') !== $zz_auth){
        $zz_query=get_curl('http://'.$zz_url.'/ZR/api/zr_check.php?url='.$host.'&authcode='.$authcode.'&version=&ZRid='.$zz_id);
            if ($zz_query = json_decode($zz_query, true)) {
        		if ($zz_query["code"] == 0) {
        			cache('zz_auth_'.$server_ip,md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM'),3600*24);
        		}else{
        			$this->error($zz_query['msg']);
        		}
        	}else{
        	    $this->error('查询授权信息失败');
        	}
        }
    }

    /**
     * 创建订单
     */
    public function createOrder($api = false)
    {
        $param = input('param.');
        $epay = new EPayService();

        if (empty($param['pid'])) {
            $this->error('商户ID不能为空');
        }
        if (empty($param['type'])) {
            $this->error('支付方式不能为空');
        }
        if (empty($param['out_trade_no'])) {
            $this->error('商户订单号不能为空');
        }
        if (empty($param['notify_url'])) {
            $this->error('服务器异步通知地址不能为空');
        }
        if (empty($param['return_url'])) {
            $this->error('页面跳转通知地址不能为空');
        }
        if (empty($param['name'])) {
            $this->error('商品名称不能为空');
        }
        if (empty($param['money'])) {
            $this->error('金额不能为空');
        }
        if (empty($param['sitename'])) {
            $param['sitename'] = '';
        }
        if (empty($param['sign'])) {
            $this->error('签名字符串不能为空');
        }
        if (empty($param['sign_type'])) {
            $this->error('签名类型不能为空');
        }

        $type = $param['type'];
        $out_trade_no = $param['out_trade_no'];
        $notify_url = $param['notify_url'];
        $return_url = $param['return_url'];
        $name = $param['name'];
        $money = $param['money'];
        $sitename = $param['sitename'];
        if (!$epay->getSignVeryfy($param, $param['sign'])) {
            $this->error('签名验证不通过');
        }

        $http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';

        if ($type == 'wxpay') {
            $type = 1;
        } else if ($type == 'alipay') {
            $type = 2;
        } else if ($type == 'qqpay') {
            $type = 3;
        } else if ($type == 'rmbpay') {
            $type = 4;
        }

        if ($param['pid'] != get_setting('epayid_ali')) {
            $this->error('该商户不存在');
        }
        
        // 使用新的限额检查逻辑
        $passageModel = new SystemPassage();
        $availablePassages = $passageModel->getAvailablePassages($type, $money);

        if (empty($availablePassages)) {
            $this->error('该通道不可用');
        }

        // 随机选择一个可用通道
        $selectedPassage = $availablePassages[array_rand($availablePassages)];
        $pid = $selectedPassage['id'];
        $passage = SystemPassage::cache(true)->find($pid);

        // 如果是微信NativeV3支付，直接调用V3服务
        if ($passage->type == 1 && $passage->code == 4) { // 支付模式:微信, 通道模式:NativeV3
            try {
                $wxpayConfig = WxpayConfig::where('passage_id', $pid)->find();
                $v3Service = new WeChatPayNativeV3Service($wxpayConfig);
                $orderData = [
                    'out_trade_no' => $out_trade_no,
                    'name' => $name,
                    'money' => $money,
                    'notify_url' => $http_type . $_SERVER['HTTP_HOST'] . '/index/WechatNativeV3Notify/notify',
                ];
                $result = $v3Service->createNativeOrder($orderData);

                if ($result['code'] == 1) {
                    // 创建系统订单
                    $this->createSystemOrder($pid, $passage->type, $passage->code, $out_trade_no, $notify_url, $return_url, $name, $money, $passage->id);
                    // 跳转到支付页面
                    return redirect('/pay/native?code_url=' . urlencode($result['code_url']) . '&trade_no=' . $out_trade_no . '&money=' . $money);
                } else {
                    $this->error($result['msg']);
                }
            } catch (\Exception $e) {
                $this->error('微信支付V3下单失败: ' . $e->getMessage());
            }
        }

        // 如果是微信JSAPI支付
        if ($passage->type == 1 && $passage->code == 5) { // 支付模式:微信, 通道模式:JSAPI
            if (empty($param['openid'])) {
                $this->error('JSAPI支付缺少openid');
            }
            try {
                $v3Service = new WeChatPayNativeV3Service($passage);
                $orderData = [
                    'out_trade_no' => $out_trade_no,
                    'name' => $name,
                    'money' => $money,
                    'notify_url' => $http_type . $_SERVER['HTTP_HOST'] . '/index/WechatNativeV3Notify/notify',
                    'openid' => $param['openid'],
                ];
                $result = $v3Service->createJsapiOrder($orderData);

                if ($result['code'] == 1) {
                    $this->createSystemOrder($pid, $passage->type, $passage->code, $out_trade_no, $notify_url, $return_url, $name, $money, $passage->id);
                    return json(['code' => 1, 'msg' => '下单成功', 'data' => $result['jsapi_params']]);
                } else {
                    $this->error($result['msg']);
                }
            } catch (\Exception $e) {
                $this->error('微信支付V3下单失败: ' . $e->getMessage());
            }
        }

        $reallyPrice = intval(bcmul($param['money'], 100));
        $tmp_price = '';
        $pay_order = SystemOrder::field('name,sitename,status,trade_no,money')->where('out_trade_no', $out_trade_no)->find();
        if ($pay_order) {
            $tmp_price = TmpPrice::where('oid', $pay_order['trade_no'])->find();
        }
        $payQf = $passage->pay_qf;
        $orderId = date("YmdHms") . mt_rand(0, 9) . mt_rand(0, 9) . mt_rand(0, 9) . mt_rand(0, 9) . mt_rand(0, 9);
        if($passage['type'] !== 2 || $passage['money_change'] === 0){
            $ok = false;
            for ($i = 0; $i < 50; $i++) {
                $tmpPrice = $reallyPrice . "-" . $type;
                if ($tmp_price && $pay_order['sitename'] == $sitename && $pay_order['name'] == $name && $pay_order['status'] != 1 && $money == $pay_order['money']) {
                    $orderId = $pay_order['trade_no'];
                    $res = TmpPrice::where([['price', '=', $tmpPrice], ['oid', '<>', $orderId]])->find();
                    if ($res) {
                        $row = false;
                    } else {
                        TmpPrice::where('oid', $orderId)->update(['price' => $tmpPrice]);
                        $row = true;
                    }
                } else {
                    $res = TmpPrice::where('price', '=', $tmpPrice)->find();
                    if(!$res){
                        $row = TmpPrice::create(['price' => $tmpPrice, 'oid' => $orderId]);
                    }else{
                        $row = false;
                    }
                }
                if ($row) {
                    $ok = true;
                    break;
                }
                if ($payQf == 0) {
                    $reallyPrice++;
                } else if ($payQf == 1) {
                    $reallyPrice--;
                }
            }
            if (!$ok) {
                $this->error('订单超出负荷，请稍后重试');
            }
        }
        $reallyPrice = bcdiv($reallyPrice, 100, 2);

        $data = [
            'pid' => $pid,
            'channel_mode' => $passage->code,
            'type' => $type,
            'trade_no' => $orderId,
            'out_trade_no' => $out_trade_no,
            'notify_url' => $notify_url,
            'return_url' => $return_url,
            'name' => $name,
            'money' => $money,
            'truemoney' => $reallyPrice,
            'email' => '',
            'pay_time' => 0,
            'status' => 0,
            'ip' => sprintf('%u', ip2long(get_real_ip())),
            'sitename' => $sitename,
            'create_time' => time()
        ];

        if ($pay_order) {
            if ($pay_order['name'] == $data['name'] && $pay_order['sitename'] == $data['sitename'] && $pay_order['status'] != 1) {
                unset($data['out_trade_no']);
                SystemOrder::where("out_trade_no", $out_trade_no)->update($data);
            } else {
                $this->error('商户订单号已存在');
            }
        } else {
            $email = get_setting('mail_order') ? get_setting('email') : '';
			if($email){
                if ($data['type'] == 1) {
                    $type = '微信';
                }
                if ($data['type'] == 2) {
                    $type = '支付宝';
                }
                if ($data['type'] == 3) {
                    $type = 'QQ';
                }
                if ($data['type'] == 4) {
                    $type = '数字人民币';
                }
				$this->sendMail($email,'订单消息通知 - '.get_setting('title'),'有一条新的订单，提交时间：'.date('Y-m-d H:i:s').'，支付方式：'.$type.'，实际金额：'.$data['truemoney'].'元，订单号：'.$data['trade_no']);
			}
            SystemOrder::create($data);
        }

        // 获取HTTP协议类型
        $url = $http_type.$_SERVER['HTTP_HOST'];
        if($api){
            // 支付类型转换映射
            $typeMap = [
                1 => 'wxpay', 2 => 'alipay', 3 => 'qqpay', 4 => 'rmbpay',
            ];
            $type = $typeMap[$data['type']] ?? 'unknown';
            
            if($passage['code'] == 1){
                $rows = cache('zz_qrcode_'.$passage['id']);
                if(!empty($rows[$passage['id']][$reallyPrice])){
                    $passage['ewm'] = $rows[$passage['id']][$reallyPrice];
                }else{
                    $qrcode = SystemQrcode::field('ewm')->where([['pid','=',$passage['id']],['money','=',$reallyPrice],['status','=',1]])->find();
                    if($qrcode){
                        $passage['ewm'] = $qrcode['ewm'];
                        $rows[$passage['id']][$reallyPrice] = $qrcode['ewm'];
                        cache('zz_qrcode_'.$passage['id'],$rows);
                    }else{
                        // 未找到该金额收款码时的处理
                    }
                }
            }
            
            if($data['type'] === 2){
                $confirm = get_setting('confirm');
                $transfer = $passage['transfer'];
                $zf_pid = $passage['zf_pid'];
                $id = SystemOrder::where('trade_no', $orderId)->value('id');
                if($confirm == 1){
                    $passage['ewm'] = $http_type.$_SERVER['HTTP_HOST'].'/index/pay/goAlipay?trade_no='.$orderId;
                }else {
                    $aliTransfer = (new AliPayService)->getAliTransfer($transfer, $reallyPrice, $id, $zf_pid);
                    if($aliTransfer){
                        $passage['ewm'] = $aliTransfer;
                    }
                }
            }
            
            $data = [
                'code' => 1,
                'msg' => '获取成功',
                'money' => $reallyPrice,
                'type' => $type,
                'qrcode' => $passage['ewm'],
                'trade_no' => $orderId,
                'out_trade_no' => $out_trade_no,
                'end_time' => $data['create_time'] + get_setting('order_timeout') * 60
            ];
            return json($data);
        }else{
            echo '<script>location.href = "'.$url.'/index/pay/page?trade_no='.$orderId.'"</script>';
        }
    }

    /**
     * 创建系统订单记录（用于V3支付）
     */
    protected function createSystemOrder($pid, $type, $code, $out_trade_no, $notify_url, $return_url, $name, $money, $passage_id)
    {
        $orderId = date("YmdHms") . mt_rand(1000, 9999);
        $data = [
            'pid' => $pid,
            'channel_mode' => $code,
            'type' => $type,
            'passage_id' => $passage_id,
            'trade_no' => $orderId,
            'out_trade_no' => $out_trade_no,
            'notify_url' => $notify_url,
            'return_url' => $return_url,
            'name' => $name,
            'money' => $money,
            'truemoney' => $money, // V3支付金额是固定的
            'status' => 0,
            'ip' => request()->ip(),
            'create_time' => time()
        ];
        SystemOrder::create($data);
    }

    /**
     * 扫码支付页面
     */
    public function native()
    {
        $code_url = input('get.code_url');
        $trade_no = input('get.trade_no');
        $money = input('get.money');
        return view('', [
            'code_url' => $code_url,
            'trade_no' => $trade_no,
            'money' => $money
        ]);
    }

    /**
     * 获取订单状态
     */
    public function get_order_status()
    {
        $trade_no = input('post.trade_no');
        if (empty($trade_no)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $order = SystemOrder::where('trade_no', $trade_no)->find();

        if ($order) {
            return json(['code' => 1, 'status' => $order->status, 'return_url' => $order->return_url]);
        } else {
            return json(['code' => 0, 'msg' => '订单不存在']);
        }
    }

    /**
     * 支付页面
     */
    public function page()
    {
        $trade_no = input('trade_no');
        $get_order = SystemOrder::field('id,type,pid,money,name,create_time,truemoney,status')->where('trade_no', $trade_no)->find();
        if (!$get_order) {
            $this->error('该订单不存在');
        }
        $passage = SystemPassage::cache(true)->findOrEmpty($get_order['pid'])->toArray();
        if (!$passage) {
            $this->error('该支付通道不存在，请重新发起支付');
        } else if ($passage['status'] == 0) {
            $this->error('该支付通道已关闭，请重新发起支付');
        }
        // 初始化支付宝配置变量
        $alipay_config = null;
        if($passage['type'] == 2){
            $alipay_config = AlipayConfig::getByPassageId($get_order['pid'],'zz_alipay_config_'.$get_order['pid']);
            if($alipay_config){
                $passage = array_merge($passage,$alipay_config->toArray());
            }
        }
        if($passage['code'] == 1){
            $rows = cache('zz_qrcode_'.$get_order['pid']);
            if(!empty($rows[$get_order['pid']][$get_order->truemoney])){
                $passage['ewm'] = $rows[$get_order['pid']][$get_order->truemoney];
            }else{
                $qrcode = SystemQrcode::field('ewm')->where([['pid','=',$get_order['pid']],['money','=',$get_order->truemoney],['status','=',1]])->find();
                if($qrcode){
                    $passage['ewm'] = $qrcode['ewm'];
                    $rows[$get_order['pid']][$get_order->truemoney] = $qrcode['ewm'];
                    cache('zz_qrcode_'.$get_order['pid'],$rows);
                }else{
                    // $this->error('未找到该金额收款码');
                }
            }
        }
        // 初始化所有支付类型变量
        $pay_type_wx = $pay_type_ali = $pay_type_qq = $pay_type_rmb = false;

        // 根据支付类型设置对应变量
        switch ($get_order['type']) {
            case 1:
                $pay_type_wx = true;
                $pay_type1 = '微信';
                $type = 'wxpay';
                break;
            case 2:
                $pay_type_ali = true;
                $pay_type1 = '支付宝';
                $type = 'alipay';
                break;
            case 3:
                $pay_type_qq = true;
                $pay_type1 = 'QQ';
                $type = 'qqpay';
                break;
            case 4:
                $pay_type_rmb = true;
                $pay_type1 = '数字人民币';
                $type = 'rmbpay';
                break;
        }

        $http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
        $url = $http_type . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        $ua = $_SERVER['HTTP_USER_AGENT'];
        $data = [
            'id' => $get_order->id,
            'pay_type_wx' => $pay_type_wx,
            'pay_type_ali' => $pay_type_ali,
            'pay_type_qq' => $pay_type_qq,
            'pay_type_rmb' => $pay_type_rmb,
            'pay_type1' => $pay_type1,
            'type' => $type,
            'price' => $get_order->money,
            'reallyPrice' => $get_order->truemoney,
            'zf_pid' => isset($passage['zf_pid']) ? $passage['zf_pid'] : '',
            'transfer' => $alipay_config && isset($alipay_config['transfer']) ? $alipay_config['transfer'] : 0,
            'ewm_type' => isset($passage['ewm_type']) ? $passage['ewm_type'] : 0,
            'payUrl' => $passage['ewm'],
            'order_id' => $trade_no,
            'http_type' => $http_type,
            'url' => $url,
            'name' => $get_order->name,
            'create_time' => $get_order->create_time,
            'isAuto' => $passage['code'] !== 1 ? 1 : 0,
            'status' => $get_order['status'],
            'ua' => $ua,
            'voice' => get_setting('voice'),
            'confirm' => get_setting('confirm'),
            'money_change' => $passage['money_change']
        ];
        $formwork = get_setting('formwork') ?: 1;
        
        return view(ROOT_PATH . 'view' . DS . 'pay' . DS . $formwork . DS . 'index.html', $data);
    }

    /**
     * 检查订单状态
     */
    public function checkOrder()
    {
        $orderId = input('orderId');
        $row = SystemOrder::where('trade_no', $orderId)->find();
        if (!$row) {
            $this->error('该订单不存在');
        }
        if ($row['status'] === 1 || $row['status'] === 2) {
            // 将数字类型转换为字符串类型
            $typeMap = [
                1 => 'wxpay',
                2 => 'alipay',
                3 => 'qqpay',
                4 => 'rmbpay',
            ];
            $row['type'] = $typeMap[$row['type']] ?? 'unknown';
            $data = [
				'pid' => get_setting('epayid_ali'),
				'type' => $row['type'],
				'out_trade_no' => $row['out_trade_no'],
				'trade_no' => $row['trade_no'],
				'name' => $row['name'],
				'money' => $row['money'],
				'trade_status' => 'TRADE_SUCCESS'
			];

            $param = (new EPayService)->buildRequestParaToString($data);
            $url = $row['return_url'];
            if (strpos($url, "?") === false) {
                $url = $url . "?" . $param;
            } else {
                $url = $url . "&" . $param;
            }
            $this->success('已支付',['url' => $url]);
        } else if ($row['status'] === 3) {
            $this->error('已过期');
        } else {
            $this->error('未支付');
        }
    }

    /**
     * 关闭过期订单
     */
    public function closeEndOrder()
    {
        $time = get_setting('order_timeout');
        $closeTime = time() - 60 * $time;
        $close_date = time();
        $res = SystemOrder::where("create_time <= " . $closeTime)
            ->where("status", 0)
            ->update(array("status" => 3, "close_time" => $close_date));
        if ($res) {
            $rows = SystemOrder::field('trade_no')->where("close_time", $close_date)->select();
            foreach ($rows as $v) {
                TmpPrice::where("oid", $v['trade_no'])->delete();
            }
        } else {
            $rows = TmpPrice::field('oid')->select();
            if ($rows) {
                foreach ($rows as $v) {
                    $re = SystemOrder::field('id')->where([['status','=',0],["trade_no", '=', $v['oid']]])->find();
                    if (!$re) {
                        TmpPrice::where("oid", $v['oid'])->delete();
                    }
                }
            }
        }
    }

    /**
     * 推送付款数据
     */
    public function appPush($type = '',$price = '',$t = '',$pid = '',$receiveOrder = '',$transMemo = '',$sign = '')
    {
        // 推送任务到队列处理
        $jobData = [
            'type' => $type ?: input("type"),
            'price' => $price ?: input("price"),
            't' => $t ?: input("t"),
            'pid' => $pid ?: input('pid'),
            'receiveOrder' => $receiveOrder ?: input('receiveOrder'),
            'transMemo' => $transMemo ?: input('transMemo'),
            'sign' => $sign ?: input("sign")
        ];

        // 推送到队列
        Queue::push('app\job\CronJob@appPushProcess', $jobData);

        return json(['code' => 1, 'msg' => '任务已推送到队列处理']);
    }

    /**
     * 更新心跳
     */
    public function appHeart()
    {
        $key = get_setting("epaykey_ali");
        $t = input("t");
        $pid = input('pid');
        $_sign = md5($t . $pid . $key);

        if ($_sign !== input("sign")) {
            return $this->error('签名校验不通过');
        }
        
        if(!$pid){
            $pid_arr = [];
            $rows = SystemPassage::field('id')->select();
            foreach ($rows as $v){
                $pid_arr[] = $v['id'];
            }
            $pid = $pid_arr;
        }
        
        $res = SystemPassage::where([['id','in',$pid],['hang_free','=',0],['status','=',1]])->update(['is_status' => 1,'run_time' => time()]);
        if ($res) {
            return json(['code' => 1,'msg' => '更新成功']);
        } else {
            return json(['code' => 0,'msg' => '更新失败或已开启免挂']);
        }
    }

    /**
     * 提交补单通知
     */
    public function submitBd()
    {
        // $maxNum = get_setting('bd_num');// 同一ip最多提交补单次数
        // $count = Db::name('pay_order')->where([['ip','=',sprintf('%u', ip2long(get_real_ip()))],['state','=',0],['send_mail' ,'=',1]])->count();
        // if($maxNum && $count >= $maxNum){
        //     return json(['code' => 0,'msg' => '提交补单的订单不能超过'.$maxNum.'个']);
        // }

        $payId = input('payId');
        $sEmail = input('email');

        if (!$payId) {
            return json(['code' => 0, 'msg' => '请传入订单号']);
        }

        if(!get_setting('mail_order_bd')){
            return json(['code' => 0,'msg' => '未开启订单补单通知']);
        }

        $row = SystemOrder::field('send_mail,truemoney,type,create_time')->where("trade_no", $payId)->find();

        if (!$row) {
            return json(['code' => 0, 'msg' => '订单号不存在']);
        }

        if ($row['send_mail'] == 1) {
            return json(['code' => 0, 'msg' => '已提交过补单，请勿再次提交']);
        }
        if (!(strtotime($row['create_time']) <= time() - 60)) {
            return json(['code' => 0, 'msg' => '请等一分钟后提交补单']);
        }
        if ($sEmail) {
            if (!preg_match('/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/', $sEmail)) {
                return json(['code' => 0, 'msg' => '邮箱格式错误']);
            }
            $row->email = $sEmail;
            $row->save();
        }

        // 支付类型名称映射
        $typeNameMap = [
            1 => '微信', 2 => '支付宝', 3 => 'QQ', 4 => '数字人民币',
        ];
        $type = $typeNameMap[$row['type']] ?? '未知支付方式';
        
        $mailContent = '有一条新的补单，提交时间：' . date('Y-m-d H:i:s') . '，支付方式：' . $type . '，实际金额：' . $row['truemoney'] . '元，订单号：' . $payId;
        $res = $this->sendMail(get_setting('email'), '订单补单通知 - ' . get_setting('title'), $mailContent);
        if ($res) {
            $row->send_mail = 1;
            $row->save();
            return json(['code' => 1, 'msg' => '提交补单成功']);
        } else {
            return json(['code' => 0, 'msg' => '提交补单失败']);
        }
    }

    /**
     * API接口支付
     */
    public function apiSubmit()
    {
        return $this->createOrder(true);
    }

    /**
     * 支付宝、QQ云端模式
     */
    public function appPast()
    {
        $key = get_setting("epaykey_ali");
        $t = input("t");
        $pid = input('pid');
        $cookie = input('cookie');
        $_sign = md5($t . $pid . $cookie . $key);

        if ($_sign !== input("sign")) {
            return $this->error('签名校验不通过');
        }

        $row = SystemPassage::field('id,is_status,hang_free,status')->find($pid);
        if ($row) {
            if($row->status === 0){
                return json(['code' => 3,'msg' => '该通道已关闭']);
            }
            if($row->hang_free === 0){
                return json(['code' => 4,'msg' => '未开启免挂']);
            }
            if($cookie){
                $row->cookie = $cookie;
                $row->is_status = 1;
                $row->ck_time = time();
                $row->save();
                return json(['code' => 2,'msg' => '更新成功']);
            }
            if($row->is_status === 1){
                return json(['code' => 1,'msg' => '正常']);
            }else{
                return json(['code' => 0,'msg' => '失效']);
            }
        } else {
            return json(['code' => 5,'msg' => '未找到该通道']);
        }
    }
    
    /**
     * 支付宝确认页
     */
    public function goAlipay()
    {
        $trade_no = input('trade_no');
        $get_order = SystemOrder::field('id,type,pid,money,name,create_time,truemoney,status')->where('trade_no', $trade_no)->find();
        if (!$get_order) {
            $this->error('该订单不存在');
        }
        $passage = SystemPassage::cache(true)->findOrEmpty($get_order['pid'])->toArray();
        if (!$passage) {
            $this->error('该支付通道不存在，请重新发起支付');
        } else if ($passage['status'] == 0) {
            $this->error('该支付通道已关闭，请重新发起支付');
        }
        
        $data = [
            'id' => $get_order->id,
            'price' => $get_order->money,
            'reallyPrice' => $get_order->truemoney,
            'zf_pid' => $passage['zf_pid'],
            'transfer' => $passage['transfer'],
            'order_id' => $trade_no,
            'status' => $get_order['status'],
            'create_time' => $get_order->create_time,
        ];
        return view(ROOT_PATH . 'view' . DS . 'pay' . DS . 'go_alipay.html', $data);
    }

    /**
     * 单笔订单查询
     */
    public function chaorder()
    {
        $param = input('param.');
        if(empty($param['order_no'])){
            $this->error('订单号不能为空');
        }
        if(empty($param['type'])){
            $this->error('订单号类型不能为空');
        }
        $type = '';
        if($param['type'] == 1){
            $type = 'trade_no';
        }else if($param['type'] == 2){
            $type = 'out_trade_no';
        }else{
            $this->error('订单号类型错误');
        }
        $row = SystemOrder::withoutField('update_time,send_mail,email')->where($type,$param['order_no'])->find();
        if($row){
            $row['ip'] = long2ip($row['ip']);
            if($row['pay_time']){
                $row['pay_time'] = date('Y-m-d H:i:s',$row['pay_time']);
            }else{
                $row['pay_time'] = '';
            }
            if($row['close_time']){
                $row['close_time'] = date('Y-m-d H:i:s',$row['close_time']);
                if($row['close_time'] === $row['create_time']){
                    $row['close_time'] = '';
                }
            }else{
                $row['close_time'] = '';
            }
            
            return json(['code' => 1,'msg' => '获取成功','data' => $row]);
        }else{
            return json(['code' => 0,'msg' => '未找到当前订单号']);
        }
    }
}
