<?php
/**
 * 企业微信消息解密诊断脚本
 * 用于诊断和修复企业微信消息解密失败问题（错误码: -40001）
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

// 引入企业微信加密库
require_once __DIR__ . '/extend/callback/ErrorCode.php';
require_once __DIR__ . '/extend/callback/SHA1.php';
require_once __DIR__ . '/extend/callback/XMLParse.php';
require_once __DIR__ . '/extend/callback/PKCS7Encoder.php';
require_once __DIR__ . '/extend/callback/Prpcrypt.php';
require_once __DIR__ . '/extend/callback/WXBizMsgCrypt.php';

use callback\WXBizMsgCrypt;

/**
 * 企业微信解密诊断类
 */
class WechatWorkDecryptDiagnose
{
    private $token;
    private $encodingAESKey;
    private $corpId;
    
    public function __construct()
    {
        // 从配置中获取企业微信参数
        $this->token = $this->getSetting('wechat_work_token');
        $this->encodingAESKey = $this->getSetting('wechat_work_encoding_aes_key');
        $this->corpId = $this->getSetting('wechat_work_corp_id');
    }
    
    /**
     * 获取配置信息
     */
    private function getSetting($key)
    {
        // 这里需要根据实际的配置获取方式来实现
        // 暂时返回空，需要手动设置
        return '';
    }
    
    /**
     * 运行完整的诊断流程
     */
    public function runDiagnosis()
    {
        echo "=== 企业微信消息解密诊断工具 ===\n\n";
        
        // 1. 检查配置
        echo "1. 检查企业微信配置...\n";
        $configResult = $this->checkConfig();
        $this->printResult($configResult);
        
        if (!$configResult['success']) {
            echo "\n❌ 配置检查失败，请先完善配置后再进行诊断\n";
            return;
        }
        
        // 2. 检查加密库
        echo "\n2. 检查企业微信加密库...\n";
        $libResult = $this->checkEncryptLibrary();
        $this->printResult($libResult);
        
        if (!$libResult['success']) {
            echo "\n❌ 加密库检查失败，请确保加密库文件完整\n";
            return;
        }
        
        // 3. 从日志中获取最近的解密失败案例
        echo "\n3. 分析最近的解密失败案例...\n";
        $logResult = $this->analyzeRecentFailures();
        $this->printResult($logResult);
        
        // 4. 提供修复建议
        echo "\n4. 修复建议:\n";
        $this->provideSuggestions();
        
        echo "\n=== 诊断完成 ===\n";
    }
    
    /**
     * 检查企业微信配置
     */
    private function checkConfig()
    {
        $result = ['success' => true, 'messages' => []];
        
        // 检查Token
        if (empty($this->token)) {
            $result['success'] = false;
            $result['messages'][] = '❌ Token未配置';
        } else {
            $result['messages'][] = '✅ Token已配置，长度: ' . strlen($this->token);
        }
        
        // 检查EncodingAESKey
        if (empty($this->encodingAESKey)) {
            $result['success'] = false;
            $result['messages'][] = '❌ EncodingAESKey未配置';
        } elseif (strlen($this->encodingAESKey) !== 43) {
            $result['success'] = false;
            $result['messages'][] = '❌ EncodingAESKey长度错误，应为43位，当前为' . strlen($this->encodingAESKey) . '位';
        } else {
            $result['messages'][] = '✅ EncodingAESKey已配置，长度正确';
        }
        
        // 检查CorpId
        if (empty($this->corpId)) {
            $result['success'] = false;
            $result['messages'][] = '❌ CorpId未配置';
        } elseif (!preg_match('/^ww[a-zA-Z0-9]{14,16}$/', $this->corpId)) {
            $result['success'] = false;
            $result['messages'][] = '❌ CorpId格式错误，应以ww开头，当前为: ' . $this->corpId;
        } else {
            $result['messages'][] = '✅ CorpId已配置，格式正确';
        }
        
        return $result;
    }
    
    /**
     * 检查加密库
     */
    private function checkEncryptLibrary()
    {
        $result = ['success' => true, 'messages' => []];
        
        $requiredFiles = [
            'extend/callback/WXBizMsgCrypt.php',
            'extend/callback/ErrorCode.php',
            'extend/callback/SHA1.php',
            'extend/callback/XMLParse.php',
            'extend/callback/PKCS7Encoder.php',
            'extend/callback/Prpcrypt.php'
        ];
        
        foreach ($requiredFiles as $file) {
            if (file_exists(__DIR__ . '/../../' . $file)) {
                $result['messages'][] = '✅ ' . $file . ' 存在';
            } else {
                $result['success'] = false;
                $result['messages'][] = '❌ ' . $file . ' 不存在';
            }
        }
        
        // 检查类是否可以正常加载
        try {
            if (class_exists('callback\\WXBizMsgCrypt')) {
                $result['messages'][] = '✅ WXBizMsgCrypt类可以正常加载';
            } else {
                $result['success'] = false;
                $result['messages'][] = '❌ WXBizMsgCrypt类无法加载';
            }
        } catch (Exception $e) {
            $result['success'] = false;
            $result['messages'][] = '❌ 加载WXBizMsgCrypt类时出错: ' . $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * 分析最近的解密失败案例
     */
    private function analyzeRecentFailures()
    {
        $result = ['success' => true, 'messages' => []];
        
        // 读取今天的日志文件
        $logFile = __DIR__ . '/runtime/log/' . date('Ym') . '/' . date('d') . '.log';
        
        if (!file_exists($logFile)) {
            $result['messages'][] = '⚠️ 今日日志文件不存在: ' . $logFile;
            return $result;
        }
        
        $logContent = file_get_contents($logFile);
        
        // 查找解密失败的记录
        if (preg_match_all('/企业微信消息解密失败，错误码: (-?\d+)/', $logContent, $matches)) {
            $errorCodes = array_count_values($matches[1]);
            $result['messages'][] = '📊 发现解密失败记录:';
            
            foreach ($errorCodes as $code => $count) {
                $errorName = $this->getErrorCodeName($code);
                $result['messages'][] = "   错误码 $code ($errorName): $count 次";
            }
        } else {
            $result['messages'][] = '✅ 今日暂无解密失败记录';
        }
        
        // 查找最近的解密参数
        if (preg_match('/企业微信消息解密参数: msgSignature=([^,]+), timestamp=([^,]+), nonce=([^,]+)/', $logContent, $matches)) {
            $result['messages'][] = '📋 最近的解密参数:';
            $result['messages'][] = '   msgSignature: ' . $matches[1];
            $result['messages'][] = '   timestamp: ' . $matches[2];
            $result['messages'][] = '   nonce: ' . $matches[3];
        }
        
        return $result;
    }
    
    /**
     * 获取错误码对应的错误名称
     */
    private function getErrorCodeName($code)
    {
        $errorNames = [
            '0' => '成功',
            '-40001' => '签名验证错误',
            '-40002' => 'XML解析失败',
            '-40003' => 'SHA加密失败',
            '-40004' => 'EncodingAESKey非法',
            '-40005' => 'CorpID校验失败',
            '-40006' => 'AES加密失败',
            '-40007' => 'AES解密失败',
            '-40008' => '解密后buffer非法',
            '-40009' => 'Base64加密失败',
            '-40010' => 'Base64解密失败',
            '-40011' => '生成XML失败'
        ];
        
        return $errorNames[$code] ?? '未知错误';
    }
    
    /**
     * 提供修复建议
     */
    private function provideSuggestions()
    {
        echo "💡 针对错误码 -40001 (签名验证错误) 的修复建议:\n\n";
        
        echo "1. 检查企业微信应用配置:\n";
        echo "   - 确保Token、EncodingAESKey、CorpId配置正确\n";
        echo "   - 检查EncodingAESKey长度是否为43位\n";
        echo "   - 检查CorpId格式是否以ww开头\n\n";
        
        echo "2. 检查企业微信管理后台设置:\n";
        echo "   - 登录企业微信管理后台\n";
        echo "   - 进入应用管理 -> 选择对应应用\n";
        echo "   - 检查接收消息设置中的Token和EncodingAESKey\n";
        echo "   - 确保与代码中的配置完全一致\n\n";
        
        echo "3. 检查回调URL配置:\n";
        echo "   - 确保回调URL可以正常访问\n";
        echo "   - 检查SSL证书是否有效（如果使用HTTPS）\n\n";
        
        echo "4. 常见问题排查:\n";
        echo "   - Token和EncodingAESKey是否包含多余的空格\n";
        echo "   - 配置是否在保存时被截断\n";
        echo "   - 是否使用了错误的应用配置\n\n";
        
        echo "5. 测试建议:\n";
        echo "   - 可以使用企业微信开发者工具进行消息推送测试\n";
        echo "   - 检查日志中的详细错误信息\n";
        echo "   - 对比期望签名和实际签名的差异\n";
    }
    
    /**
     * 打印结果
     */
    private function printResult($result)
    {
        foreach ($result['messages'] as $message) {
            echo "   $message\n";
        }
    }
    
    /**
     * 手动设置配置进行测试
     */
    public function setConfig($token, $encodingAESKey, $corpId)
    {
        $this->token = $token;
        $this->encodingAESKey = $encodingAESKey;
        $this->corpId = $corpId;
    }
    
    /**
     * 测试特定的解密参数
     */
    public function testDecrypt($msgSignature, $timestamp, $nonce, $encryptMsg)
    {
        echo "\n=== 测试解密参数 ===\n";
        echo "msgSignature: $msgSignature\n";
        echo "timestamp: $timestamp\n";
        echo "nonce: $nonce\n";
        echo "encryptMsg长度: " . strlen($encryptMsg) . "\n\n";
        
        try {
            $wxCrypt = new WXBizMsgCrypt($this->token, $this->encodingAESKey, $this->corpId);
            
            $decryptMsg = '';
            $errCode = $wxCrypt->DecryptMsg($msgSignature, $timestamp, $nonce, $encryptMsg, $decryptMsg);
            
            if ($errCode === 0) {
                echo "✅ 解密成功!\n";
                echo "解密后的消息: $decryptMsg\n";
            } else {
                echo "❌ 解密失败，错误码: $errCode\n";
                echo "错误说明: " . $this->getErrorCodeName($errCode) . "\n";
                
                if ($errCode == -40001) {
                    $this->analyzeSignatureError($msgSignature, $timestamp, $nonce, $encryptMsg);
                }
            }
            
        } catch (Exception $e) {
            echo "❌ 解密过程中发生异常: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 分析签名验证错误
     */
    private function analyzeSignatureError($msgSignature, $timestamp, $nonce, $encryptMsg)
    {
        echo "\n🔍 分析签名验证错误:\n";
        
        // 解析XML获取Encrypt内容
        $encrypt = '';
        if (strpos($encryptMsg, '<xml>') !== false) {
            $xml = simplexml_load_string($encryptMsg);
            $encrypt = (string)$xml->Encrypt;
        } else {
            $encrypt = $encryptMsg;
        }
        
        echo "Encrypt内容前50字符: " . substr($encrypt, 0, 50) . "...\n";
        
        // 尝试不同的签名组合
        $combinations = [
            [$this->token, $timestamp, $nonce, $encrypt],
            [$this->token, $timestamp, $nonce],
            [$timestamp, $nonce, $encrypt, $this->token],
            [$timestamp, $nonce, $this->token]
        ];
        
        foreach ($combinations as $i => $params) {
            sort($params, SORT_STRING);
            $sortedStr = implode($params);
            $signature = sha1($sortedStr);
            
            echo "组合" . ($i + 1) . " (" . implode(', ', array_keys($params)) . "): $signature";
            if ($signature === $msgSignature) {
                echo " ✅ 匹配!";
            }
            echo "\n";
        }
        
        echo "\n实际签名: $msgSignature\n";
    }
}

// 如果直接运行此脚本
if (php_sapi_name() === 'cli') {
    echo "请手动设置企业微信配置后运行诊断\n";
    echo "示例用法:\n";
    echo "\$diagnose = new WechatWorkDecryptDiagnose();\n";
    echo "\$diagnose->setConfig('your_token', 'your_encoding_aes_key', 'your_corp_id');\n";
    echo "\$diagnose->runDiagnosis();\n";
} else {
    // Web访问时的处理
    $diagnose = new WechatWorkDecryptDiagnose();
    
    // 如果有POST参数，则进行测试
    if ($_POST) {
        if (isset($_POST['config'])) {
            $diagnose->setConfig($_POST['token'], $_POST['encoding_aes_key'], $_POST['corp_id']);
            $diagnose->runDiagnosis();
        } elseif (isset($_POST['test'])) {
            $diagnose->setConfig($_POST['token'], $_POST['encoding_aes_key'], $_POST['corp_id']);
            $diagnose->testDecrypt($_POST['msg_signature'], $_POST['timestamp'], $_POST['nonce'], $_POST['encrypt_msg']);
        }
    } else {
        // 显示配置表单
        echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>企业微信解密诊断工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>企业微信解密诊断工具</h1>
    
    <div class="section">
        <h2>配置诊断</h2>
        <form method="post">
            <div class="form-group">
                <label>Token:</label>
                <input type="text" name="token" required>
            </div>
            <div class="form-group">
                <label>EncodingAESKey:</label>
                <input type="text" name="encoding_aes_key" required>
            </div>
            <div class="form-group">
                <label>CorpId:</label>
                <input type="text" name="corp_id" required>
            </div>
            <button type="submit" name="config" value="1">运行配置诊断</button>
        </form>
    </div>
    
    <div class="section">
        <h2>解密测试</h2>
        <form method="post">
            <div class="form-group">
                <label>Token:</label>
                <input type="text" name="token" required>
            </div>
            <div class="form-group">
                <label>EncodingAESKey:</label>
                <input type="text" name="encoding_aes_key" required>
            </div>
            <div class="form-group">
                <label>CorpId:</label>
                <input type="text" name="corp_id" required>
            </div>
            <div class="form-group">
                <label>消息签名 (msg_signature):</label>
                <input type="text" name="msg_signature" required>
            </div>
            <div class="form-group">
                <label>时间戳 (timestamp):</label>
                <input type="text" name="timestamp" required>
            </div>
            <div class="form-group">
                <label>随机数 (nonce):</label>
                <input type="text" name="nonce" required>
            </div>
            <div class="form-group">
                <label>加密消息 (encrypt_msg):</label>
                <textarea name="encrypt_msg" rows="10" required></textarea>
            </div>
            <button type="submit" name="test" value="1">测试解密</button>
        </form>
    </div>
</body>
</html>';
    }
}
