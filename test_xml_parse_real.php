<?php
require_once 'extend/callback/XMLParse.php';
require_once 'extend/callback/ErrorCode.php';

use callback\XMLParse;
use callback\ErrorCode;

// 从日志中获取的真实XML数据
$xmlData = '<xml><ToUserName><![CDATA[ww666a49fdd2b8622e]]></ToUserName><Encrypt><![CDATA[ZY/42jihKIHDrUV7yCAHg0QkxR8BGTXQlarCef2s0X+NfkR/9pA/gp9nK9rWBzY/m9FVXyL5j4FozOTebW9XCVo1cOE/Mw7PeCLJY1K3NZXs8akiHLrFI4IjK1Uo29YFyK6EscBoI2sE01v48BTBhrAj7Nrug3wHgvmqNme+VCiXmS4b6mNXIxf8jQu8pfxPZ7HQG+RB2WlgzRK2QbQ7GSWIO747dyim5waa9W3cPmflnUvpDwAUZT8RHk8mSAzHwERVQxMJ6VSBumbj3E9U1F3D2lnPRlHbwvTOWIJ/lNMI9Ctak9o4Vi0dL4aErauv//XWj0P0aVlal4FtmgmYslc6e24AX0PkraqdVtrsL5y8Hny28hSIW4IA/XENgpY7pZCy3koGnViHpq5nQgGGqRt+sogrjTGZWddNBdHeGT/2KbERR9X+04kDisTAjIB9ge9XYWO6PM5gyOhkJ/GDSl2a5TPyvtE0GsJLSpXYqz/7cq9ZEekCGQYxicGJyrPK]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>';

echo "测试XMLParse类解析真实XML数据\n";
echo "XML数据: " . $xmlData . "\n\n";

$xmlparse = new XMLParse();

try {
    $result = $xmlparse->extract($xmlData);
    
    echo "解析结果:\n";
    echo "错误码: " . $result[0] . "\n";
    
    if ($result[0] === 0) {
        echo "加密内容: " . $result[1] . "\n";
        echo "加密内容长度: " . strlen($result[1]) . "\n";
        echo "XML解析成功！\n";
    } else {
        echo "XML解析失败，错误码: " . $result[0] . "\n";
        
        // 检查错误码含义
        switch ($result[0]) {
            case ErrorCode::$ParseXmlError:
                echo "错误类型: XML解析错误\n";
                break;
            case ErrorCode::$ComputeSignatureError:
                echo "错误类型: 签名计算错误\n";
                break;
            case ErrorCode::$ValidateSignatureError:
                echo "错误类型: 签名验证错误\n";
                break;
            case ErrorCode::$IllegalAesKey:
                echo "错误类型: 非法AES密钥\n";
                break;
            case ErrorCode::$IllegalBuffer:
                echo "错误类型: 非法缓冲区\n";
                break;
            case ErrorCode::$DecodeBase64Error:
                echo "错误类型: Base64解码错误\n";
                break;
            default:
                echo "错误类型: 未知错误\n";
                break;
        }
    }
} catch (Exception $e) {
    echo "异常: " . $e->getMessage() . "\n";
}

// 测试第二个XML数据
$xmlData2 = '<xml><ToUserName><![CDATA[ww666a49fdd2b8622e]]></ToUserName><Encrypt><![CDATA[nR8hVEbd+5KPygZesJeQquBHu+7gbH+sR37Ov6rmr70avgVERlvGGm+4Ftg36WFAbWuQlWbyebQQvCtH3ufZfvQkhHgjT7w/ewE41qMRS0J0AZDR/KMEpFUfDEofc700EAENiOQ8hb+lIz58dxgKHw/tlNrhCXxnw/vDyBgFlPI6zigkZ5Rhtd6z2YUqalTVhG/TP8GM3fcUCQJwsmzzydzSxHXbSKYYMfAGM9LI3EZdMsEOfRs7S4b+PFu7PeiLI5w9DUVCh2otINCBLc1z++TzzGwe9wY+zdcYyL5f9pGXHOlE3cixKOTdh4iuIMzPtvOyXA74QgqaNRsCqzptuApIjuC9fSA/cvbTAQ8Ye6/+ffJP4TLJdHoIFPW++lS7elGRrCimvdR9n/cXduSPJEWCnIqMVPd6GPYOGysE2IRf7M37sEw9BS5zhcxXzGp/W+HCHEvleFhSM30fpKXvvs2BQ3Xv6TIF2ag9DQINJOrZ3oIjap1aYoRxT1CidJfJ]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>';

echo "\n\n测试第二个XML数据\n";
echo "XML数据: " . $xmlData2 . "\n\n";

try {
    $result = $xmlparse->extract($xmlData2);
    
    echo "解析结果:\n";
    echo "错误码: " . $result[0] . "\n";
    
    if ($result[0] === 0) {
        echo "加密内容: " . $result[1] . "\n";
        echo "加密内容长度: " . strlen($result[1]) . "\n";
        echo "XML解析成功！\n";
    } else {
        echo "XML解析失败，错误码: " . $result[0] . "\n";
    }
} catch (Exception $e) {
    echo "异常: " . $e->getMessage() . "\n";
}