<?php
require_once 'extend/callback/WXBizMsgCrypt.php';
require_once 'extend/callback/ErrorCode.php';
require_once 'extend/callback/SHA1.php';
require_once 'extend/callback/Prpcrypt.php';
require_once 'extend/callback/PKCS7Encoder.php';
require_once 'extend/callback/XMLParse.php';

use callback\WXBizMsgCrypt;
use callback\ErrorCode;
use callback\SHA1;
use callback\Prpcrypt;
use callback\PKCS7Encoder;
use callback\XMLParse;

echo "=== 企业微信解密测试 - 使用真实的加密消息 ===\n\n";

// 企业微信配置参数（从数据库中获取）
$corpId = 'ww666a49fdd2b8622e';
$token = 'XjQLm4iGs8YtmGQ3oE9Q0Op';
$encodingAESKey = 'X198BDkRlpg375z4hwakFKwp8MgyLa7GsRleDi8WgVw';

echo "1. 企业微信配置参数:\n";
echo "   - CorpId: " . ($corpId ? "✅ 已配置 (" . substr($corpId, 0, 8) . "...)" : "❌ 未配置") . "\n";
echo "   - Token: " . ($token ? "✅ 已配置 (" . substr($token, 0, 10) . "...)" : "❌ 未配置") . "\n";
echo "   - EncodingAESKey: " . ($encodingAESKey ? "✅ 已配置 (" . substr($encodingAESKey, 0, 10) . "...)" : "❌ 未配置") . "\n";
echo "   - EncodingAESKey长度: " . strlen($encodingAESKey) . " 字符\n";

// 检查EncodingAESKey格式
if ($encodingAESKey) {
    $decoded = base64_decode($encodingAESKey);
    echo "   - EncodingAESKey解码后长度: " . strlen($decoded) . " 字节\n";
    if (strlen($decoded) == 32) {
        echo "   - ✅ EncodingAESKey格式正确（32字节）\n";
    } else {
        echo "   - ❌ EncodingAESKey格式错误（应为32字节）\n";
    }
}

echo "\n";


if (!$corpId || !$token || !$encodingAESKey) {
    echo "❌ 企业微信配置不完整，无法进行测试\n";
    exit(1);
}

// 从日志中提取的真实加密消息
$encryptedMessages = [
    [
        'xml' => '<xml><ToUserName><![CDATA[ww666a49fdd2b8622e]]></ToUserName><Encrypt><![CDATA[06g5pkwviNIxd2DM0P2l+9JEQPF5p7zyk3bxuvzTmbTxDB1GbdM5D21qotq9QNLNzHQ6DR8MoMEkRJ2gZiQr+QdNNoBZWv8f5/MoT4pyn4RIMb5lWoim1QDDqDPFR8XUQES3Z+ApWPbYLbcyxLH0CQevZiYYF0Xz6BOWNZd8TzHIZ5Iz92D5H6m+fi6XxeJ4edzAaduSIt8sdqbLRwUArCBpYOPkhN+LmGrlhoFSGKGdr6uwhyaCvEqyYqfIlzzXVC9fmJcHaYDuDPM1bCyygaxWwi/vhEJFrV+UX+9rh0LSci4oQ2pNTaWNNDhdZBh29QqIE3erT/EQrn1+ei0nV8O0RneJXj6SgE+6GwgHQXYqvJfQeWtBY4m0qNMORaDdESxdIodOZyCwGKWLwPg7k5N1Dc/mKMFvoLHFGiCdPjxpmqCuxVrs2mbmhuCxoxYF1aShqHX/GKy7YpbNEMPkiJVrE+ZAavukDMQvTCTU+eu9A1B9jp65cngAwE3qPNos]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>',
        'msg_signature' => 'test_signature_1',
        'timestamp' => '1755741977',
        'nonce' => '12345678'
    ],
    [
        'xml' => '<xml><ToUserName><![CDATA[ww666a49fdd2b8622e]]></ToUserName><Encrypt><![CDATA[zAqQl5Xj23sPY/BdeuA6X5uP+3T3iG3ELAnVLPrTRBtU5IvlmYv2pdFzLzaZSzFMekSF1s2cY2aiifvfj40iJTfivMNsunVjkJ+ryKqZ7mDgx+hKxFOIgv04MRHe2+razno3tOqwN3lLo3cY9n3HiXDfGg7F+Q62JIbK0mkvFFmtlCNncEYw+K2dR1QPusxbrLWdNm9xmGsGN6g3hbrBFYBkZ1gN0S26DYZiONcikIIQOcaEho6zBA7E3YpgQik6Ds4GQu9Oat1XfRJn0OxSKLvy6sDdTAp30sYVa3oFH7cE7LzBkL54W8wxjdPASXSV/7AtF2LghdYtyz+gyokm+rDSdglHKyNZ6it59aY/ZcFUPQUt/lk/AzylsOzyrT9oVq8vjr6qpVQx3GruZRYT+BsGsRUveRgp0nECpMhjPYQemc/7APe14uuwPBCUdMgSW4OcvY/XaH0DOnX3fck+2MhtqoIKHfoQBWfTdbpk1C5HrF8mXri0mKVsjkNBWOGE]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>',
        'msg_signature' => 'test_signature_2',
        'timestamp' => '1755741984',
        'nonce' => '87654321'
    ]
];

$wxcpt = new WXBizMsgCrypt($token, $encodingAESKey, $corpId);

echo "2. 测试解密真实加密消息:\n\n";

foreach ($encryptedMessages as $index => $message) {
    echo "--- 测试消息 " . ($index + 1) . " ---\n";
    echo "   时间戳: " . $message['timestamp'] . "\n";
    echo "   随机数: " . $message['nonce'] . "\n";
    echo "   消息签名: " . $message['msg_signature'] . "\n";
    echo "   XML长度: " . strlen($message['xml']) . " 字符\n";
    
    // 首先提取加密内容
    $xml = simplexml_load_string($message['xml']);
    $encrypt = (string)$xml->Encrypt;
    
    // 生成正确的签名
    $sha1 = new SHA1();
    $array = $sha1->getSHA1($token, $message['timestamp'], $message['nonce'], $encrypt);
    $ret = $array[0];
    
    if ($ret == 0) {
        $correctSignature = $array[1];
        echo "   生成的正确签名: " . $correctSignature . "\n";
        echo "   原始测试签名: " . $message['msg_signature'] . "\n";
        
        // 使用正确的签名进行解密
        $msg = '';
        $errCode = $wxcpt->DecryptMsg($correctSignature, $message['timestamp'], $message['nonce'], $message['xml'], $msg);
    } else {
        echo "   ❌ 签名生成失败，错误码: " . $ret . "\n";
        $errCode = $ret;
    }
    
    if ($errCode == 0) {
        echo "   ✅ 解密成功\n";
        echo "   解密后的消息:\n";
        echo "   " . $msg . "\n\n";
    } else {
        echo "   ❌ 解密失败，错误码: " . $errCode . "\n";
        echo "   💡 错误说明: ";
        switch ($errCode) {
            case -40001:
                echo "签名验证错误\n";
                break;
            case -40002:
                echo "XML解析失败\n";
                break;
            case -40003:
                echo "SHA加密生成签名失败\n";
                break;
            case -40004:
                echo "EncodingAESKey非法\n";
                break;
            case -40005:
                echo "CorpID校验错误\n";
                break;
            case -40006:
                echo "AES加密失败\n";
                break;
            case -40007:
                echo "AES解密失败\n";
                break;
            case -40008:
                echo "解密后得到的buffer非法\n";
                break;
            case -40009:
                echo "Base64加密失败\n";
                break;
            case -40010:
                echo "Base64解密失败\n";
                break;
            case -40011:
                echo "生成XML失败\n";
                break;
            default:
                echo "未知错误\n";
                break;
        }
        
        // 如果是签名验证错误，尝试生成正确的签名
        if ($errCode == -40001) {
            echo "\n   🔍 尝试生成正确的签名:\n";
            
            // 提取加密内容
            $xml = simplexml_load_string($message['xml']);
            if ($xml && isset($xml->Encrypt)) {
                $encrypt = (string)$xml->Encrypt;
                echo "   提取的加密内容: " . substr($encrypt, 0, 50) . "...\n";
                
                // 生成签名
                $sha1 = new SHA1();
                $array = $sha1->getSHA1($token, $message['timestamp'], $message['nonce'], $encrypt);
                $ret = $array[0];
                
                if ($ret == 0) {
                    $correctSignature = $array[1];
                    echo "   生成的签名: " . $correctSignature . "\n";
                    echo "   原始签名: " . $message['msg_signature'] . "\n";
                    
                    // 使用正确的签名重新测试
                    $msg = '';
                    $errCode = $wxcpt->DecryptMsg($correctSignature, $message['timestamp'], $message['nonce'], $message['xml'], $msg);
                    echo "   使用正确签名重新测试结果: " . ($errCode == 0 ? "✅ 成功" : "❌ 失败 (错误码: " . $errCode . ")") . "\n";
                } else {
                    echo "   ❌ 签名生成失败，错误码: " . $ret . "\n";
                }
            } else {
                echo "   ❌ 无法从XML中提取加密内容\n";
            }
        }
        
        echo "\n";
    }
}

echo "=== 测试完成 ===\n";