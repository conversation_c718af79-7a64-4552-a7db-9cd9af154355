<?php /*a:1:{s:59:"E:\phpEnv\www\pay.cn\app\admin\view\system\config\edit.html";i:1755593553;}*/ ?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>配置管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/admin/lib/layui-v2.11.4/css/layui.css" media="all">
    <link rel="stylesheet" href="/static/admin/css/public.css" media="all">
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main" id="app">

            <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                <ul class="layui-tab-title">
                    <li class="layui-this">网站设置</li>
                    <li>LOGO设置</li>
                    <li>邮箱设置</li>
                    <li>通道设置</li>
                    <li>企业微信</li>
                    <li>上传设置</li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <form id="app-form" class="layui-form layuimini-form">

                            <div class="layui-form-item">
                                <label class="layui-form-label required">网站链接</label>
                                <div class="layui-input-block">
                                    <input type="text" name="url" lay-verify="required" placeholder="请输入网站描述" value="<?php echo htmlentities((string) $url); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label required">网站标题</label>
                                <div class="layui-input-block">
                                    <input type="text" name="title" lay-verify="required" placeholder="请输入网站标题" value="<?php echo htmlentities((string) $title); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">网站关键词</label>
                                <div class="layui-input-block">
                                    <input type="text" name="keywords" placeholder="请输入网站关键词" value="<?php echo htmlentities((string) $keywords); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">网站描述</label>
                                <div class="layui-input-block">
                                    <input type="text" name="description" placeholder="请输入网站描述" value="<?php echo htmlentities((string) $description); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">网站底部</label>
                                <div class="layui-input-block">
                                    <input type="text" name="footer" placeholder="请输入网站底部" value="<?php echo htmlentities((string) $footer); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">手机号</label>
                                <div class="layui-input-block">
                                    <input type="text" name="phone" placeholder="请输入手机号" value="<?php echo htmlentities((string) $phone); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">QQ号</label>
                                <div class="layui-input-block">
                                    <input type="text" name="qq" placeholder="请输入QQ号" value="<?php echo htmlentities((string) $qq); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">微信号</label>
                                <div class="layui-input-block">
                                    <input type="text" name="weixin" placeholder="请输入微信号" value="<?php echo htmlentities((string) $weixin); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">邮箱</label>
                                <div class="layui-input-block">
                                    <input type="text" name="email" placeholder="请输入邮箱" value="<?php echo htmlentities((string) $email); ?>" class="layui-input">
                                    <tip>设置了邮箱才会接收到通知！</tip>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">首页跳转地址</label>
                                <div class="layui-input-block">
                                    <input type="text" name="home_url" placeholder="请输入首页跳转地址" value="<?php echo htmlentities((string) $home_url); ?>" class="layui-input">
                                    <tip>设置后首页不能直接打开，可以防止被查到是支付站</tip>
                                </div>
                            </div>

                            <div class="hr-line"></div>
                            <div class="layui-form-item text-center">
                                <div class="layui-input-block">
                                    <button class="layui-btn layui-btn-normal" lay-submit lay-filter="saveBtn">确认保存</button>
                                </div>
                            </div>

                        </form>
                    </div>

                    <div class="layui-tab-item">
                        <form id="app-form" class="layui-form layuimini-form">
                    
                            <div class="layui-form-item">
                                <label class="layui-form-label required">LOGO标题</label>
                                <div class="layui-input-block">
                                    <input type="text" name="logo_title" lay-verify="required" placeholder="请输入LOGO标题" value="<?php echo htmlentities((string) $logo_title); ?>"
                                        class="layui-input">
                                    <tip>用于显示在LOGO图标旁边的标题文字</tip>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label required">LOGO图标</label>
                                <div class="layui-input-block layuimini-upload">
                                    <input name="logo_image" lay-verify="required" class="layui-input layui-col-xs6" placeholder="请上传LOGO图标"
                                        value="<?php echo htmlentities((string) $logo_image); ?>">
                                    <div class="layuimini-upload-btn">
                                        <span><a class="layui-btn" data-upload="logo_image" data-upload-number="one" data-upload-icon="image" data-upload-exts="png|jpg|ico|jpeg" data-upload-path="/static/admin/images/logo.png"><i class="fa fa-upload"></i> 上传</a></span>
                                    </div>
                                    <tip>建议尺寸：64x64像素，支持格式：png、jpg、ico、jpeg</tip>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label required">浏览器图标</label>
                                <div class="layui-input-block layuimini-upload">
                                    <input name="favicon" lay-verify="required" class="layui-input layui-col-xs6" placeholder="请上传浏览器图标"
                                        value="<?php echo htmlentities((string) $favicon); ?>">
                                    <div class="layuimini-upload-btn">
                                        <span><a class="layui-btn" data-upload="favicon" data-upload-number="one" data-upload-icon="image" data-upload-exts="ico|png" data-upload-path="/favicon.ico"><i class="fa fa-upload"></i> 上传</a></span>
                                    </div>
                                    <tip>建议尺寸：16x16或32x32像素，推荐格式：ico，也支持png</tip>
                                </div>
                            </div>

                            <div class="hr-line"></div>
                            <div class="layui-form-item text-center">
                                <div class="layui-input-block">
                                    <button class="layui-btn layui-btn-normal" lay-submit
                                        lay-filter="saveBtn">确认保存</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="layui-tab-item">
                        <form id="app-form" class="layui-form layuimini-form">
                            <input type="hidden" name="e_status" value="<?php echo htmlentities((string) $e_status); ?>">
                            <div class="layui-form-item">
                                <label class="layui-form-label required">状态</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" lay-skin="switch" lay-text="开启|禁用" lay-filter="e_status" <?php if($e_status == 1): ?>checked<?php endif; ?>>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">邮件编码</label>
                                <div class="layui-input-block">
                                    <select name="e_char_set">
                                        <option value="utf-8" <?php if($e_char_set === 'utf-8'): ?>selected<?php endif; ?>>utf-8</option>
                                        <option value="8bit" <?php if($e_char_set === '8bit'): ?>selected<?php endif; ?>>8bit</option>
                                        <option value="7bit" <?php if($e_char_set === '7bit'): ?>selected<?php endif; ?>>7bit</option>
                                        <option value="binary" <?php if($e_char_set === 'binary'): ?>selected<?php endif; ?>>binary</option>
                                        <option value="base64" <?php if($e_char_set === 'base64'): ?>selected<?php endif; ?>>base64</option>
                                        <option value="quoted-printable" <?php if($e_char_set === 'quoted-printable'): ?>selected<?php endif; ?>>quoted-printable</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">SMTP 端口</label>
                                <div class="layui-input-block">
                                    <input type="text" name="e_port" placeholder="请输入SMTP 端口" value="<?php echo htmlentities((string) $e_port); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">SMTP 主机名</label>
                                <div class="layui-input-block">
                                    <input type="text" name="e_host" placeholder="请输入SMTP 主机名" value="<?php echo htmlentities((string) $e_host); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">SMTP 用户名</label>
                                <div class="layui-input-block">
                                    <input type="text" name="e_username" placeholder="请输入SMTP 用户名" value="<?php echo htmlentities((string) $e_username); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">SMTP 密码</label>
                                <div class="layui-input-block">
                                    <input type="password" name="e_password" placeholder="请输入SMTP 密码" value="<?php echo htmlentities((string) $e_password); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">SMTP SSL类型</label>
                                <div class="layui-input-block">
                                    <select name="e_smtp_secure">
                                        <option value="" <?php if($e_smtp_secure === ''): ?>selected<?php endif; ?>>无</option>
                                        <option value="ssl" <?php if($e_smtp_secure === 'ssl'): ?>selected<?php endif; ?>>ssl</option>
                                        <option value="tls" <?php if($e_smtp_secure === 'tls'): ?>selected<?php endif; ?>>tls</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">系统邮件名</label>
                                <div class="layui-input-block">
                                    <input type="text" name="e_from_name" placeholder="请输入系统邮件名" value="<?php echo htmlentities((string) $e_from_name); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">系统邮箱名</label>
                                <div class="layui-input-block">
                                    <input type="text" name="e_set_from" placeholder="请输入系统邮箱名" value="<?php echo htmlentities((string) $e_set_from); ?>" class="layui-input">
                                </div>
                            </div>

                            <div class="hr-line"></div>
                            <div class="layui-form-item text-center">
                                <div class="layui-input-block">
                                    <button class="layui-btn layui-btn-normal" lay-submit lay-filter="saveBtn">确认保存</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="layui-tab-item">
                        <form id="app-form" class="layui-form layuimini-form">
                            <input type="hidden" name="voice" value="<?php echo htmlentities((string) $voice); ?>">
                            <input type="hidden" name="offline" value="<?php echo htmlentities((string) $offline); ?>">
                            <input type="hidden" name="popup" value="<?php echo htmlentities((string) $popup); ?>">
                            <input type="hidden" name="mail_login" value="<?php echo htmlentities((string) $mail_login); ?>">
                            <input type="hidden" name="mail_order" value="<?php echo htmlentities((string) $mail_order); ?>">
                            <input type="hidden" name="mail_order_bd" value="<?php echo htmlentities((string) $mail_order_bd); ?>">
                            <input type="hidden" name="mail_order_hd" value="<?php echo htmlentities((string) $mail_order_hd); ?>">
                            <input type="hidden" name="confirm" value="<?php echo htmlentities((string) $confirm); ?>">
                            <div class="layui-form-item">
                                <label class="layui-form-label">语音提醒</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" lay-skin="switch" lay-text="开启|禁用" lay-filter="voice" <?php if($voice == 1): ?>checked<?php endif; ?>>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">语音提醒内容</label>
                                <div class="layui-input-block">
                                    <input type="text" name="voice_text" placeholder="请输入语音提醒内容" value="<?php echo htmlentities((string) $voice_text); ?>" class="layui-input">
                                    <tip>语音提醒内容，{money}是金额</tip>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">自定义语音播报接口</label>
                                <div class="layui-input-block">
                                    <input type="text" name="voice_text_api" placeholder="请输入自定义语音播报接口" value="<?php echo htmlentities((string) $voice_text_api); ?>" class="layui-input">
                                    <tip>自定义语音播报接口地址，{text}是文本内容</tip>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">掉线提醒</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" lay-skin="switch" lay-text="开启|禁用" lay-filter="offline" <?php if($offline == 1): ?>checked<?php endif; ?>>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">付款弹窗提示</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" lay-skin="switch" lay-text="开启|禁用" lay-filter="popup" <?php if($popup == 1): ?>checked<?php endif; ?>>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">付款弹窗内容</label>
                                <div class="layui-input-block">
                                    <input type="text" name="prompt" placeholder="请输入付款弹窗内容" value="<?php echo htmlentities((string) $prompt); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">邮件登录提示</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" lay-skin="switch" lay-text="开启|禁用" lay-filter="mail_login" <?php if($mail_login == 1): ?>checked<?php endif; ?>>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">订单消息通知</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" lay-skin="switch" lay-text="开启|禁用" lay-filter="mail_order" <?php if($mail_order == 1): ?>checked<?php endif; ?>>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">订单补单通知</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" lay-skin="switch" lay-text="开启|禁用" lay-filter="mail_order_bd" <?php if($mail_order_bd == 1): ?>checked<?php endif; ?>>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">订单回调通知</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" lay-skin="switch" lay-text="开启|禁用" lay-filter="mail_order_hd" <?php if($mail_order_hd == 1): ?>checked<?php endif; ?>>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">支付宝确认页</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" lay-skin="switch" lay-text="开启|禁用" lay-filter="confirm" <?php if($confirm == 1): ?>checked<?php endif; ?>>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">收银模板</label>
                                <div class="layui-input-block">
                                    <select name="formwork">
                                        <option value="1" <?php if($formwork == 1): ?>selected<?php endif; ?>>模板一</option>
                                        <option value="2" <?php if($formwork == 2): ?>selected<?php endif; ?>>模板二</option>
                                        <option value="3" <?php if($formwork == 3): ?>selected<?php endif; ?>>模板三</option>
                                        <option value="4" <?php if($formwork == 4): ?>selected<?php endif; ?>>模板四</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">订单超时时间</label>
                                <div class="layui-input-block">
                                    <input type="number" name="order_timeout" placeholder="请输入订单超时时间，单位：分" value="<?php echo htmlentities((string) $order_timeout); ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">超时跳转地址</label>
                                <div class="layui-input-block">
                                    <input type="text" name="timeout_url" placeholder="请输入超时跳转地址" value="<?php echo htmlentities((string) $timeout_url); ?>" class="layui-input">
                                </div>
                            </div>

                            <div class="hr-line"></div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">二维码解码接口地址</label>
                                <div class="layui-input-block">
                                    <input type="text" name="qrcode_decode_url" placeholder="例如：https://api.example.com/decode?img=" value="<?php echo isset($qrcode_decode_url) ? htmlentities((string) $qrcode_decode_url) : ''; ?>" class="layui-input">
                                    <tip>输入完整的接口地址，图片URL会自动追加到地址末尾</tip>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">解码结果字段</label>
                                <div class="layui-input-block">
                                    <input type="text" name="qrcode_decode_field" placeholder="例如：data 或 result.content" value="<?php echo isset($qrcode_decode_field) ? htmlentities((string) $qrcode_decode_field) : 'data'; ?>" class="layui-input">
                                    <tip>指定接口返回JSON中包含解码结果的字段名，支持多级字段（用点分隔）</tip>
                                </div>
                            </div>

                            <div class="hr-line"></div>
                            <div class="layui-form-item text-center">
                                <div class="layui-input-block">
                                    <button class="layui-btn layui-btn-normal" lay-submit lay-filter="saveBtn">确认保存</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 企业微信配置 -->
                    <div class="layui-tab-item">
                        <form id="app-form" class="layui-form layuimini-form">
                            <input type="hidden" name="wechat_work_enabled" value="<?php echo htmlentities((string) $wechat_work_enabled); ?>">

                            <div class="layui-form-item">
                                <label class="layui-form-label">状态</label>
                                <div class="layui-input-block">
                                    <input type="checkbox" lay-skin="switch" lay-text="开启|关闭" lay-filter="wechat_work_enabled" <?php if($wechat_work_enabled == 1): ?>checked<?php endif; ?>>
                                    <tip>开启后支付页面将显示企业微信客服按钮</tip>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label required">企业ID</label>
                                <div class="layui-input-block">
                                    <input type="text" name="wechat_work_corp_id" value="<?php echo htmlentities((string) $wechat_work_corp_id); ?>" lay-verify="required" placeholder="请输入企业微信的企业ID" class="layui-input">
                                    <tip>在企业微信管理后台 → 我的企业 → 企业信息中获取</tip>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label required">应用Secret</label>
                                <div class="layui-input-block">
                                    <input type="password" name="wechat_work_corp_secret" value="<?php echo htmlentities((string) $wechat_work_corp_secret); ?>" lay-verify="required" placeholder="请输入应用的Secret" class="layui-input">
                                    <tip>在企业微信管理后台 → 应用管理 → 自建应用中获取</tip>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label required">应用ID</label>
                                <div class="layui-input-block">
                                    <input type="text" name="wechat_work_agent_id" value="<?php echo htmlentities((string) $wechat_work_agent_id); ?>" lay-verify="required" placeholder="请输入应用的AgentId" class="layui-input">
                                    <tip>在企业微信管理后台 → 应用管理 → 自建应用中获取</tip>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label required">Token</label>
                                <div class="layui-input-block">
                                    <input type="text" name="wechat_work_token" value="<?php echo htmlentities((string) $wechat_work_token); ?>" lay-verify="required" placeholder="请输入企业微信应用的Token" class="layui-input">
                                    <tip>在企业微信管理后台 → 应用管理 → 自建应用 → 接收消息设置中获取</tip>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label required">EncodingAESKey</label>
                                <div class="layui-input-block">
                                    <input type="text" name="wechat_work_encoding_aes_key" value="<?php echo htmlentities((string) $wechat_work_encoding_aes_key); ?>" lay-verify="required" placeholder="请输入企业微信应用的EncodingAESKey" class="layui-input">
                                    <tip>在企业微信管理后台 → 应用管理 → 自建应用 → 接收消息设置中获取</tip>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">回调URL</label>
                                <div class="layui-input-block">
                                    <input type="text" value="<?php echo htmlentities((string) $_SERVER['REQUEST_SCHEME']); ?>://<?php echo htmlentities((string) $_SERVER['HTTP_HOST']); ?>/wechatwork" readonly class="layui-input" style="background-color: #f5f5f5;">
                                    <tip>请将此URL配置到企业微信应用的接收消息设置中</tip>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label">测试连接</label>
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-normal" id="testWechatWork">测试企业微信连接</button>
                                    <tip>测试企业微信配置是否正确</tip>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn layui-btn-normal" lay-submit lay-filter="saveBtn">确认保存</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="layui-tab-item">
                        <form id="app-form" class="layui-form layuimini-form">

                            <div class="layui-form-item">
                                <label class="layui-form-label required">存储方式</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="upload_type" lay-filter="upload_type" value="0"
                                        title="本地存储" checked>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label required">允许类型</label>
                                <div class="layui-input-block">
                                    <input type="text" name="fileext" class="layui-input" lay-verify="required"
                                        placeholder="请输入允许类型"
                                        value="<?php echo htmlentities((string) $fileext); ?>">
                                    <tip>英文逗号做分隔符。</tip>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label required">允许大小</label>
                                <div class="layui-input-block">
                                    <input type="text" name="filesize" class="layui-input"
                                        lay-verify="required" placeholder="请输入允许上传大小"
                                        value="<?php echo htmlentities((string) $filesize); ?>">
                                    <tip>设置允许上传大小，单位：M。</tip>
                                </div>
                            </div>

                            <div class="hr-line"></div>
                            <div class="layui-form-item text-center">
                                <div class="layui-input-block">
                                    <button class="layui-btn layui-btn-normal" lay-submit
                                        lay-filter="saveBtn">确认保存</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                </div>
            </div>

        </div>
    </div>

    <script src="/static/admin/lib/layui-v2.11.4/layui.js" charset="utf-8"></script>
    <script src="/static/admin/js/common.js"></script>
    <script>
        layui.use(['form','upload'], function () {
            var form = layui.form,
                layer = layui.layer,
                $ = layui.$;
                upload = layui.upload;

            //监听提交
            form.on('submit(saveBtn)', function (data) {
                $.post("<?php echo url('system.config/edit'); ?>", data.field, function (res) {
                    icon = res.code == 1 ? 1 : 2;
                    layer.msg(res.msg, { time: 1500, icon: icon }, function () {
                        if (res.code == 1) {
                            location.reload();
                        }
                    })
                })

                return false;
            });

            // 监听状态事件
            form.on('switch(e_status)', function(obj) {
                // console.log(obj);
                // layer.tips(this.value + ' ' + this.name + '：'+ obj.elem.checked, obj.othis);
                var status = obj.elem.checked ? 1 : 0;
                $('[name="e_status"]').val(status);
            });
            form.on('switch(voice)', function(obj) {
                // console.log(obj);
                // layer.tips(this.value + ' ' + this.name + '：'+ obj.elem.checked, obj.othis);
                var status = obj.elem.checked ? 1 : 0;
                $('[name="voice"]').val(status);
            });
            form.on('switch(offline)', function(obj) {
                // console.log(obj);
                // layer.tips(this.value + ' ' + this.name + '：'+ obj.elem.checked, obj.othis);
                var status = obj.elem.checked ? 1 : 0;
                $('[name="offline"]').val(status);
            });
            form.on('switch(popup)', function(obj) {
                // console.log(obj);
                // layer.tips(this.value + ' ' + this.name + '：'+ obj.elem.checked, obj.othis);
                var status = obj.elem.checked ? 1 : 0;
                $('[name="popup"]').val(status);
            });
            form.on('switch(mail_login)', function(obj) {
                // console.log(obj);
                // layer.tips(this.value + ' ' + this.name + '：'+ obj.elem.checked, obj.othis);
                var status = obj.elem.checked ? 1 : 0;
                $('[name="mail_login"]').val(status);
            });
            form.on('switch(mail_order)', function(obj) {
                // console.log(obj);
                // layer.tips(this.value + ' ' + this.name + '：'+ obj.elem.checked, obj.othis);
                var status = obj.elem.checked ? 1 : 0;
                $('[name="mail_order"]').val(status);
            });
            form.on('switch(mail_order_bd)', function(obj) {
                // console.log(obj);
                // layer.tips(this.value + ' ' + this.name + '：'+ obj.elem.checked, obj.othis);
                var status = obj.elem.checked ? 1 : 0;
                $('[name="mail_order_bd"]').val(status);
            });
            form.on('switch(mail_order_hd)', function(obj) {
                // console.log(obj);
                // layer.tips(this.value + ' ' + this.name + '：'+ obj.elem.checked, obj.othis);
                var status = obj.elem.checked ? 1 : 0;
                $('[name="mail_order_hd"]').val(status);
            });
            form.on('switch(confirm)', function(obj) {
                // console.log(obj);
                // layer.tips(this.value + ' ' + this.name + '：'+ obj.elem.checked, obj.othis);
                var status = obj.elem.checked ? 1 : 0;
                $('[name="confirm"]').val(status);
            });

            // 监听单选框
            form.on('radio(upload_type)',function(data){
                    // console.log(data)
                    if(data.value == 0){
                        $('.qnoss,.txcos,.alioss').hide().find('input').removeAttr('lay-verify');
                    }else if(data.value == 1){
                        $('.qnoss').show().find('input').attr('lay-verify','required');
                        $('.txcos,.alioss').hide().find('input').removeAttr('lay-verify');
                    }else if(data.value == 2){
                        $('.txcos').show().find('input').attr('lay-verify','required');
                        $('.qnoss,.alioss').hide().find('input').removeAttr('lay-verify');
                    }else if(data.value == 3){
                        $('.alioss').show().find('input').attr('lay-verify','required');
                        $('.qnoss,.txcos').hide().find('input').removeAttr('lay-verify');
                    }
                })

            // 初始化LOGO图标上传
            upload.render({
                elem: '[data-upload="logo_image"]',
                url: '<?php echo url("index/upload"); ?>',
                data: {
                    type: 'logo_image',
                    filename: $('[name="logo_image"]').val()
                },
                accept: 'images',
                acceptMime: 'image/*',
                exts: 'png|jpg|ico|jpeg',
                size: 5120, // 5MB
                before: function(obj){
                    layer.load();
                },
                done: function(res){
                    layer.closeAll('loading');
                    if(res.code == 1){
                        layer.msg('上传成功', {icon: 1},function(){
                            location.reload();
                        });
                    } else {
                        layer.msg('上传失败：' + res.msg, {icon: 2});
                    }
                },
                error: function(){
                    layer.closeAll('loading');
                    layer.msg('上传出错', {icon: 2});
                }
            });
            
            // 初始化浏览器图标上传
            upload.render({
                elem: '[data-upload="favicon"]',
                url: '<?php echo url("index/upload"); ?>',
                data: {
                    type: 'favicon',
                    filename: $('[name="favicon"]').val()
                },
                accept: 'images',
                acceptMime: 'image/*',
                exts: 'ico|png',
                size: 1024, // 1MB
                before: function(obj){
                    layer.load();
                },
                done: function(res){
                    layer.closeAll('loading');
                    if(res.code == 1){
                        layer.msg('上传成功', {icon: 1});
                    } else {
                        layer.msg('上传失败：' + res.msg, {icon: 2});
                    }
                },
                error: function(){
                    layer.closeAll('loading');
                    layer.msg('上传出错', {icon: 2});
                }
            });

            // 企业微信测试连接
            $('#testWechatWork').click(function() {
                var corpId = $('input[name="wechat_work_corp_id"]').val();
                var corpSecret = $('input[name="wechat_work_corp_secret"]').val();
                var agentId = $('input[name="wechat_work_agent_id"]').val();
                var token = $('input[name="wechat_work_token"]').val();
                var encodingAESKey = $('input[name="wechat_work_encoding_aes_key"]').val();

                if (!corpId || !corpSecret || !agentId || !token || !encodingAESKey) {
                    layer.msg('请先填写完整的企业微信配置信息', {icon: 2});
                    return;
                }

                var btn = $(this);
                var originalText = btn.text();
                btn.text('测试中...').prop('disabled', true);

                $.ajax({
                    url: '/wechatwork/testConnection',
                    type: 'POST',
                    data: {
                        corp_id: corpId,
                        corp_secret: corpSecret,
                        agent_id: agentId,
                        token: token,
                        encoding_aes_key: encodingAESKey
                    },
                    success: function(data) {
                        btn.text(originalText).prop('disabled', false);
                        if (data.code === 1) {
                            layer.msg('连接成功！' + (data.token ? ' Token: ' + data.token : ''), {icon: 1});
                        } else {
                            layer.msg('连接失败：' + (data.msg || '未知错误'), {icon: 2});
                        }
                    },
                    error: function() {
                        btn.text(originalText).prop('disabled', false);
                        layer.msg('网络错误，请重试', {icon: 2});
                    }
                });
            });

            // 企业微信开关监听
            form.on('switch(wechat_work_enabled)', function(data) {
                var isEnabled = data.elem.checked;
                var requiredInputs = $('input[name="wechat_work_corp_id"], input[name="wechat_work_corp_secret"], input[name="wechat_work_agent_id"], input[name="wechat_work_token"], input[name="wechat_work_encoding_aes_key"]');

                if (isEnabled) {
                    $('[name="wechat_work_enabled"]').val(1);
                    requiredInputs.attr('lay-verify', 'required');
                    // layer.msg('已启用企业微信客服，请确保配置信息完整', {icon: 1});
                } else {
                    $('[name="wechat_work_enabled"]').val(0);
                    requiredInputs.removeAttr('lay-verify');
                    // layer.msg('已关闭企业微信客服', {icon: 0});
                }

                form.render();
            });

        });
    </script>
</body>

</html>
</html>