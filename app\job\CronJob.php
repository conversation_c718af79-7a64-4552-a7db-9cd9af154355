<?php

namespace app\job;

use app\admin\model\SystemOrder;
use app\admin\model\SystemPassage;
use app\common\service\AliPayService;
use app\common\service\QqPayService;
use think\queue\Job;
use PHPMailer\PHPMailer\PHPMailer;

class CronJob
{
    /**
     * 主定时任务 - 对应原来的index方法
     */
    public function mainCron(Job $job, $data)
    {
        try {
            $email = get_setting('offline') ? get_setting('email') : '';

            // 同步在线状态
            $rows = SystemPassage::field('id,hang_free')->where([['run_time', '<', time() - 60], ['is_status', '=', 1], ['status', '=', 1]])->select();
            $pid = [];
            if ($rows) {
                foreach ($rows as $v) {
                    $v->is_status = 0;
                    $v->save();
                    if ($v->hang_free === 0) {
                        $pid[] = $v->id;
                    }
                }
            }

            // 免挂掉线通知
            $mail = cache('zz_SystemPassage_mail') ?: [];
            if ($mail) {
                foreach ($mail as $k => $v) {
                    if ($v < time() - 60 * 5) {
                        $pid[] = $k;
                        unset($mail[$k]);
                    }
                }
            }
            cache('zz_SystemPassage_mail', $mail);

            // 邮件掉线提醒
            if ($email && $pid) {
                $this->sendMail($email, '支付通道掉线提醒 - ' . get_setting('title'), '通道id：' . implode(',', $pid) . '，已掉线，请及时处理！');
            }

            // 每日清理过期订单（仅在0点执行）
            if (date('H:i:s') >= '00:00:00' && date('H:i:s') <= '00:00:05') {
                $time = strtotime('-7 days');
                SystemOrder::where([['status', '=', 3], ['create_time', '<', $time]])->delete();
            }

            // 关闭过期订单
            $this->closeEndOrder();

            // 回调失败的订单再次执行
            $order = new SystemOrder();
            $rows = $order->field('id')->where('status', 2)->where('create_time', '>=', time() - 60)->where('create_time', '<=', time())->select()->toArray();
            if ($rows) {
                foreach ($rows as $v) {
                    $order->orderPayHandle($v['id'], false);
                }
            }

            $job->delete();
        } catch (\Exception $e) {
            $job->failed($e);
            $errorMsg = 'MainCron job failed: ' . $e->getMessage();
            echo $errorMsg;
            
            // 保存错误信息到缓存，有效期1天
            $cronErrors = cache('cron_job_errors') ?: [];
            $cronErrors[] = [
                'time' => date('Y-m-d H:i:s'),
                'job' => 'MainCron',
                'message' => $errorMsg
            ];
            cache('cron_job_errors', $cronErrors, 86400); // 86400秒 = 1天
            
            $job->delete();
        }
    }

    /**
     * 支付宝回调监控 - 对应原来的ali方法
     */
    public function aliMonitor(Job $job, $data)
    {
        try {
            $aliExecutionTime = 10;
            $aliPay = new AliPayService;
            $rows = SystemPassage::field('id,balance,cookie,old_balance,zf_pid,avoid_ck,app_id,public_key,private_key,hang_free,money_change,run_time,is_status')->where([['type', '=', 2], ['is_status|avoid_ck', '<>', 0], ['status', '=', 1]])->select();

            if ($rows) {
                foreach ($rows as $v) {
                    if ($v['hang_free'] === 0) {
                        echo '通道ID：' . $v['id'] . '，未开启免挂' . PHP_EOL;
                        continue;
                    }

                    if ($v['avoid_ck'] !== 0) {
                        require_once public_path() . 'alipay-sdk-php-all-master/v2/aop/AopClient.php';
                        $aop = new \AopClient();
                        $aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
                        $aop->appId = $v['app_id'];
                        $aop->rsaPrivateKey = $v['private_key'];
                        $aop->alipayrsaPublicKey = $v['public_key'];
                        $aop->apiVersion = '1.0';
                        $aop->signType = 'RSA2';
                        $aop->postCharset = 'UTF-8';
                        $aop->format = 'json';

                        if ($v['avoid_ck'] === 1) {
                            require_once public_path() . 'alipay-sdk-php-all-master/v2/aop/request/AlipayDataBillBalanceQueryRequest.php';
                            $request = new \AlipayDataBillBalanceQueryRequest();
                            $request->setBizContent("{" .
                                "  \"bill_user_id\":\"{$v['zf_pid']}\"" .
                                "}");
                            $result = $aop->execute($request);
                            $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";
                            $resultCode = $result->$responseNode->code;

                            if (!empty($resultCode) && $resultCode == 10000) {
                                $money = $result->$responseNode->total_amount;
                                if ($money === '') {
                                    $cron = cache('zz_SystemPassage_cron_' . $v->id) ?: 0;
                                    if ($cron <= 10) {
                                        cache('zz_SystemPassage_cron_' . $v->id, $cron + 1);
                                    } else {
                                        $mail = cache('zz_SystemPassage_mail') ?: [];
                                        $mail[$v->id] = time();
                                        cache('zz_SystemPassage_mail', $mail);

                                        cache('zz_SystemPassage_cron_' . $v->id, null);
                                        $v->is_status = 0;
                                        $v->save();
                                    }
                                } else {
                                    if (!$v->old_balance) {
                                        $v->old_balance = $money;
                                    } else {
                                        if ($money != $v->old_balance) {
                                            $price = $money - $v->old_balance;
                                            if ($price > 0) {
                                                $pid = $v->id;
                                                $price = round($price, 2);
                                                $key = get_setting('epaykey_ali');
                                                $t = time();
                                                $type = 2;
                                                $sign = md5($type . $price . $t . $pid . $key);
                                                // 推送到队列处理
                                                $jobData = [
                                                    'type' => $type,
                                                    'price' => $price,
                                                    't' => $t,
                                                    'pid' => $pid,
                                                    'receiveOrder' => '',
                                                    'transMemo' => '',
                                                    'sign' => $sign
                                                ];
                                                \think\facade\Queue::push('app\job\CronJob@appPushProcess', $jobData);
                                            }
                                            $v->old_balance = $money;
                                        }
                                    }
                                    if ($v->is_status === 0) {
                                        $v->is_status = 1;
                                    }
                                    $v->balance = $money;
                                    $v->run_time = time();
                                    $v->save();

                                    cache('zz_SystemPassage_cron_' . $v->id, null);

                                    $mail = cache('zz_SystemPassage_mail') ?: [];
                                    if (!empty($mail[$v->id])) {
                                        unset($mail[$v->id]);
                                        cache('zz_SystemPassage_mail', $mail);
                                    }
                                }
                            } else {
                                echo "支付宝免CK-余额模式获取失败，原因：" . $result->$responseNode->msg;
                            }
                        } else {
                            if ($v->run_time < time() - $aliExecutionTime) {
                                require_once public_path() . 'alipay-sdk-php-all-master/v2/aop/request/AlipayDataBillAccountlogQueryRequest.php';
                                $request = new \AlipayDataBillAccountlogQueryRequest();
                                $request->setBizContent("{" .
                                    "  \"start_time\":\"" . date('Y-m-d H:i:s', time() - $aliExecutionTime) . "\"," .
                                    "  \"end_time\":\"" . date('Y-m-d H:i:s') . "\"," .
                                    "  \"bill_user_id\":\"{$v['zf_pid']}\"" .
                                    "}");
                                $result = $aop->execute($request);
                                $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";
                                $resultCode = $result->$responseNode->code;

                                if (!empty($resultCode) && $resultCode == 10000) {
                                    $detail_list = !empty($result->$responseNode->detail_list) ? $result->$responseNode->detail_list : [];
                                    if ($detail_list) {
                                        foreach ($detail_list as $k2 => $v2) {
                                            if ($k2 === 0 && $v2->balance !== '') {
                                                $v->balance = $v2->balance;
                                            }
                                            $price = $v2->trans_amount;
                                            if ($price > 0) {
                                                $pid = $v->id;
                                                $price = round($price, 2);
                                                $key = get_setting('epaykey_ali');
                                                $t = time();
                                                $type = 2;
                                                // $trans_memo = !empty($v2->trans_memo) && $v['money_change'] === 1 && is_numeric($v2->trans_memo) ? $v2->trans_memo : '';
                                                $sign = md5($type . $price . $t . $pid . $trans_memo . $key);
                                                // 推送到队列处理
                                                $jobData = [
                                                    'type' => $type,
                                                    'price' => $price,
                                                    't' => $t,
                                                    'pid' => $pid,
                                                    'receiveOrder' => $v2->alipay_order_no,
                                                    'transMemo' => $v2->trans_memo,
                                                    'sign' => $sign
                                                ];
                                                \think\facade\Queue::push('app\job\CronJob@appPushProcess', $jobData);
                                            }
                                        }
                                    }
                                    cache('zz_SystemPassage_cron_' . $v->id, null);

                                    $mail = cache('zz_SystemPassage_mail') ?: [];
                                    if (!empty($mail[$v->id])) {
                                        unset($mail[$v->id]);
                                        cache('zz_SystemPassage_mail', $mail);
                                    }
                                    if ($v->is_status === 0) {
                                        $v->is_status = 1;
                                    }
                                    $v->run_time = time();
                                    $v->save();
                                } else {
                                    echo "支付宝免CK-账单模式获取失败，原因：" . $result->$responseNode->msg;
                                    $cron = cache('zz_SystemPassage_cron_' . $v->id) ?: 0;
                                    if ($cron <= 5) {
                                        cache('zz_SystemPassage_cron_' . $v->id, $cron + 1);
                                    } else {
                                        $mail = cache('zz_SystemPassage_mail') ?: [];
                                        $mail[$v->id] = time();
                                        cache('zz_SystemPassage_mail', $mail);

                                        cache('zz_SystemPassage_cron_' . $v->id, null);
                                        $v->is_status = 0;
                                        $v->save();
                                    }
                                }
                            }
                        }
                    } else if ($v->cookie) {
                        $cookie = base64_decode($v->cookie);
                        $arr = $aliPay->GetMoney($cookie, $v->zf_pid);
                        $money = '';
                        if ($arr['status']) {
                            $money = $arr['money'];
                        }
                        if ($money === '') {
                            $cron = cache('zz_SystemPassage_cron_' . $v->id) ?: 0;
                            if ($cron <= 10) {
                                cache('zz_SystemPassage_cron_' . $v->id, $cron + 1);
                            } else {
                                $mail = cache('zz_SystemPassage_mail') ?: [];
                                $mail[$v->id] = time();
                                cache('zz_SystemPassage_mail', $mail);

                                cache('zz_SystemPassage_cron_' . $v->id, null);
                                $v->is_status = 0;
                                $v->save();
                            }
                        } else {
                            if (!$v->old_balance) {
                                $v->old_balance = $money;
                            } else {
                                if ($money != $v->old_balance) {
                                    $price = $money - $v->old_balance;
                                    if ($price > 0) {
                                        $pid = $v->id;
                                        $price = round($price, 2);
                                        $key = get_setting('epaykey_ali');
                                        $t = time();
                                        $type = 2;
                                        $sign = md5($type . $price . $t . $pid . $key);
                                        // 推送到队列处理
                                        $jobData = [
                                            'type' => $type,
                                            'price' => $price,
                                            't' => $t,
                                            'pid' => $pid,
                                            'receiveOrder' => '',
                                            'transMemo' => '',
                                            'sign' => $sign
                                        ];
                                        \think\facade\Queue::push('app\job\CronJob@appPushProcess', $jobData);
                                    }
                                    $v->old_balance = $money;
                                }
                            }
                            $v->balance = $money;
                            $v->run_time = time();
                            $v->save();

                            $ali_home = cache('zz_ali_home_' . $v->id) ?: 0;
                            if ($ali_home < time() - 60) {
                                $aliPay->BaoHuo($cookie);
                                cache('zz_ali_home_' . $v->id, time());
                            }

                            cache('zz_SystemPassage_cron_' . $v->id, null);

                            $mail = cache('zz_SystemPassage_mail') ?: [];
                            if (!empty($mail[$v->id])) {
                                unset($mail[$v->id]);
                                cache('zz_SystemPassage_mail', $mail);
                            }
                        }
                    }
                }
            }

            $job->delete();
        } catch (\Exception $e) {
            $job->failed($e);
            $errorMsg = 'AliMonitor job failed: ' . $e->getMessage();
            echo $errorMsg;
            
            // 保存错误信息到缓存，有效期1天
            $cronErrors = cache('cron_job_errors') ?: [];
            $cronErrors[] = [
                'time' => date('Y-m-d H:i:s'),
                'job' => 'AliMonitor',
                'message' => $errorMsg
            ];
            cache('cron_job_errors', $cronErrors, 86400); // 86400秒 = 1天
            
            $job->delete();
        }
    }

    /**
     * QQ回调监控 - 对应原来的qq方法
     */
    public function qqMonitor(Job $job, $data)
    {
        try {
            $qqExecutionTime = 10;
            $qqPay = new QqPayService;
            $rows = SystemPassage::field('id,balance,cookie,old_balance,run_time,hang_free')->where([['type', '=', 3], ['is_status', '=', 1], ['status', '=', 1]])->select();

            if ($rows) {
                foreach ($rows as $v) {
                    if ($v['hang_free'] === 0) {
                        echo '通道ID：' . $v['id'] . '，未开启免挂' . PHP_EOL;
                        continue;
                    }
                    if ($v->run_time < time() - $qqExecutionTime) {
                        $cookie = base64_decode($v->cookie);
                        preg_match('/uin=o(.*?);/', $cookie, $uin);
                        $qq = $uin[1];
                        $json = $qqPay->GetOrder($qq, $cookie);
                        $arr = json_decode($json, true);

                        if ($arr['retcode'] != '0' && $arr['retmsg'] != 'OK') {
                            $cron = cache('zz_SystemPassage_cron_' . $v->id) ?: 0;
                            if ($cron <= 5) {
                                cache('zz_SystemPassage_cron_' . $v->id, $cron + 1);
                            } else {
                                $mail = cache('zz_SystemPassage_mail') ?: [];
                                $mail[$v->id] = time();
                                cache('zz_SystemPassage_mail', $mail);

                                cache('zz_SystemPassage_cron_' . $v->id, null);
                                $v->is_status = 0;
                                $v->save();
                            }
                        } else if (!empty($arr['records'])) {
                            $time = time();
                            foreach ($arr['records'] as $v2) {
                                if (strtotime($v2['pay_time']) <= $time && strtotime($v2['pay_time']) >= $time - $qqExecutionTime) {
                                    $price = $v2['price'] / 100;
                                    if ($price > 0) {
                                        $pid = $v->id;
                                        $price = round($price, 2);
                                        $key = get_setting('epaykey_ali');
                                        $t = time();
                                        $type = 3;
                                        $sign = md5($type . $price . $t . $pid . $key);
                                        // 推送到队列处理
                                        $jobData = [
                                            'type' => $type,
                                            'price' => $price,
                                            't' => $t,
                                            'pid' => $pid,
                                            'receiveOrder' => $v->sp_billno,
                                            'transMemo' => $v->desc,
                                            'sign' => $sign
                                        ];
                                        \think\facade\Queue::push('app\job\CronJob@appPushProcess', $jobData);
                                    }
                                }
                            }

                            cache('zz_SystemPassage_cron_' . $v->id, null);

                            $mail = cache('zz_SystemPassage_mail') ?: [];
                            if (!empty($mail[$v->id])) {
                                unset($mail[$v->id]);
                                cache('zz_SystemPassage_mail', $mail);
                            }

                            $v->run_time = time();
                            $v->save();
                        }
                    }
                }
            }

            $job->delete();
        } catch (\Exception $e) {
            $job->failed($e);
            $errorMsg = 'QqMonitor job failed: ' . $e->getMessage();
            echo $errorMsg;
            
            // 保存错误信息到缓存，有效期1天
            $cronErrors = cache('cron_job_errors') ?: [];
            $cronErrors[] = [
                'time' => date('Y-m-d H:i:s'),
                'job' => 'QqMonitor',
                'message' => $errorMsg
            ];
            cache('cron_job_errors', $cronErrors, 86400); // 86400秒 = 1天
            
            $job->delete();
        }
    }

    /**
     * 关闭过期订单 - 来自Pay控制器的closeEndOrder方法
     */
    protected function closeEndOrder()
    {
        $time = get_setting('order_timeout');
        $closeTime = time() - 60 * $time;
        $close_date = time();
        $res = SystemOrder::where("create_time <= " . $closeTime)
            ->where("status", 0)
            ->update(array("status" => 3, "close_time" => $close_date));
        if ($res) {
            $rows = SystemOrder::field('trade_no')->where("close_time", $close_date)->select();
            foreach ($rows as $v) {
                \app\admin\model\TmpPrice::where("oid", $v['trade_no'])->delete();
            }
        } else {
            $rows = \app\admin\model\TmpPrice::field('oid')->select();
            if ($rows) {
                foreach ($rows as $v) {
                    $re = SystemOrder::field('id')->where([['status', '=', 0], ["trade_no", '=', $v['oid']]])->find();
                    if (!$re) {
                        \app\admin\model\TmpPrice::where("oid", $v['oid'])->delete();
                    }
                }
            }
        }
    }



    /**
     * 处理付款数据推送
     */
    public function appPushProcess(Job $job, $data)
    {
        try {
            $key = get_setting("epaykey_ali");
            $type = $data['type'];
            $price = $data['price'];
            $t = $data['t'];
            $pid = $data['pid'];
            $receiveOrder = $data['receiveOrder'];
            $transMemo = $data['transMemo'];
            $sign = $data['sign'];

            $_sign = md5($type . $price . $t . $pid . $receiveOrder . $transMemo . $key);

            if ($_sign !== $sign) {
                echo 'AppPush签名校验不通过';
                $job->delete();
                return;
            }

            if (!$pid) {
                $row = SystemPassage::field('id')->where('type', $type)->order('id', 'asc')->find();
                if ($row) {
                    $pid = $row['id'];
                }
            }

            $orderData = [
                ['truemoney', '=', $price],
                ['status', '=', 0],
                ['type', '=', $type],
                ['pid', '=', $pid]
            ];

            $passage = SystemPassage::cache(true)->find($pid);
            if (!$passage) {
                echo '该支付通道不存在';
                $job->delete();
                return;
            }
            if ($type == 2 && $passage['money_change'] === 1) {
                $orderData[] = ['id', '=', $transMemo];
            }

            $row = SystemOrder::where($orderData)->find();
            if ($row) {
                \app\admin\model\TmpPrice::where("oid", $row['trade_no'])->delete();

                if ($row->status === 0) {
                    SystemPassage::where('id', $pid)->update(['succcount' => \think\facade\Db::raw('succcount+1'), 'succprice' => \think\facade\Db::raw('succprice+' . $price)]);
                    // 更新通道今日金额
                    $passageModel = new \app\admin\model\SystemPassage();
                    $passageModel->updateTodayAmount($pid, $price);
                }
                $row->status = 1;
                $row->pay_time = time();
                $row->close_time = time();
                $row->save();

                if ($type == 1) {
                    $type = 'wxpay';
                }
                if ($type == 2) {
                    $type = 'alipay';
                }
                if ($type == 3) {
                    $type = 'qqpay';
                }
                if ($type == 4) {
                    $type = 'rmbpay';
                }
                $callbackData = [
                    'pid' => get_setting('epayid_ali'),
                    'type' => $type,
                    'out_trade_no' => $row['out_trade_no'],
                    'trade_no' => $row['trade_no'],
                    'name' => $row['name'],
                    'money' => $row['money'],
                    'trade_status' => 'TRADE_SUCCESS'
                ];

                $param = (new \app\common\service\EPayService())->buildRequestParaToString($callbackData);
                $url = $row['notify_url'];
                if (strpos($url, "?") === false) {
                    $url = $url . "?" . $param;
                } else {
                    $url = $url . "&" . $param;
                }

                $re = get_curl($url);
                if (get_setting('mail_order_hd')) {
                    // 支付类型名称映射
                    $typeNameMap = [
                        1 => '微信', 2 => '支付宝', 3 => 'QQ', 4 => '数字人民币',
                    ];
                    $typeName = $typeNameMap[$row['type']] ?? '未知支付方式';
                    if (get_setting('email')) {
                        $this->sendMail(get_setting('email'), '订单回调通知 - ' . get_setting('title'), '你好，你的订单已经执行异步回调处理，回调时间：' . date('Y-m-d H:i:s') . '，支付方式：' . $typeName . '，回调返回信息：' . $re . '，回调链接：' . $url);
                    }
                }
                if (stripos($re, 'success') !== false) {
                    echo 'AppPush回调成功';
                } else {
                    $row->status = 2;
                    $row->save();
                    echo 'AppPush回调失败';
                }
            } else {
                echo 'AppPush未找到对应金额订单信息';
            }

            $job->delete();
        } catch (\Exception $e) {
            $job->failed($e);
            $errorMsg = 'AppPushProcess job failed: ' . $e->getMessage();
            echo $errorMsg;
            
            // 保存错误信息到缓存，有效期1天
            $cronErrors = cache('cron_job_errors') ?: [];
            $cronErrors[] = [
                'time' => date('Y-m-d H:i:s'),
                'job' => 'AppPushProcess',
                'message' => $errorMsg
            ];
            cache('cron_job_errors', $cronErrors, 86400); // 86400秒 = 1天
            
            $job->delete();
        }
    }

    /**
     * 邮件发送 - 来自CommonController的sendMail方法
     */
    protected function sendMail($to, $title, $content)
    {
        if (!get_setting('e_status')) {
            return false;
        }

        $mail = new PHPMailer();

        $mail->isSMTP(); // 使用SMTP服务
        $mail->CharSet = get_setting('e_char_set'); // 编码格式为utf8，不设置编码的话，中文会出现乱码
        $mail->Host = get_setting('e_host'); // 发送方的SMTP服务器地址
        $mail->SMTPAuth = true; // 是否使用身份验证
        $mail->FromName = get_setting('e_from_name'); //设置发件人姓名（昵称） 任意内容，显示在收件人邮件的发件人邮箱地址前的发件人姓名
        $mail->Username = get_setting('e_username'); // 发送方的163邮箱用户名，就是你申请163的SMTP服务使用的163邮箱 
        $mail->Password = get_setting('e_password'); // 发送方的邮箱密码，注意用163邮箱这里填写的是"客户端授权密码"而不是邮箱的登录密码！ 
        $mail->SMTPSecure = get_setting('e_smtp_secure'); // 使用ssl协议方式 
        $mail->Port = get_setting('e_port'); // 163邮箱的ssl协议方式端口号是465/994

        $mail->setFrom(get_setting('e_set_from')); // 设置发件人信息，如邮件格式说明中的发件人，这里会显示为Mailer(<EMAIL>），Mailer是当做名字显示
        if (is_array($to)) {
            foreach ($to as $v) {
                $mail->addAddress($v);
            }
        } else {
            $mail->addAddress($to); // 设置收件人信息，如邮件格式说明中的收件人，这里会显示为Liang(<EMAIL>)
        }

        $mail->Subject = $title; // 邮件标题
        $mail->Body = $content; // 邮件正文

        if (!$mail->send()) { // 发送邮件
            return false;
        } else {
            return true;
        }
    }
}
