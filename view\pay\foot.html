<script>
    var time = new Date().getTime() - <?php echo strtotime($create_time);?> * 1000;
    time = time / 1000;
    time = <?php echo get_setting('order_timeout');?> * 60 - time;

    if (<?php echo $status;?> == 3) {
        time = 0;
    }
    timer(time);
    check();

    function check() {
        $.post("/checkOrder", "orderId=<?php echo $order_id;?>", function(res) {
            // console.log(res);
            if (res.code == 1) {
                $("#divTime").html("<small style='color:red; font-size:22px'>"+ res.msg +"</small>");
                $("#qrcode").html('<img id="qrcode_load" src="/static/pay/3/img/pay_ok.png">');
                $(".zf-status").html('<button>已支付</button>');
                layer.msg("支付成功，正在跳转中...",{time: 1500,icon: 1},function(){
                    userAgent = navigator.userAgent;
                    if(userAgent.indexOf('Alipay') != -1){
                        alert(res.msg);
                        window.open("","_self").close()
                    }else{
                        location.href = res.data.url;
                    }
                });
            } else {
                if (res.msg == "已过期") {
                    intDiff = 0;
                    var timeout_url = '<?php echo get_setting('timeout_url');?>';
                    if(timeout_url){
                        location.href = timeout_url;
                    }
                } else {
                    setTimeout("check()", 1500);
                }
            }
        })
    }
    
    <?php if($price != $reallyPrice){?>
    var index = layer.alert('温馨提示：<?php echo $price;?>元已被其他用户占用，请您务必付款<font color=red><?php echo $reallyPrice;?></font>元，<font color=red>多付一分或者少付一分都不能到账</font>！', {
        icon: 7,
        skin: 'layer-ext-moon', //该皮肤由layer.seaning.com友情扩展。关于皮肤的扩展规则，去这里查阅
    },function(){
        layer.close(index);
        <?php if(get_setting('popup')){?>
        var text = '<?php echo get_setting('prompt');?>';
        if(text){
            layer.alert(text); 
        }
        <?php }?>
    });
    <?php }else{?>
        <?php if(get_setting('popup')){?>
        var text = '<?php echo get_setting('prompt');?>';
        if(text){
            layer.alert(text); 
        }
        <?php }?>
    <?php }?>

    function copyText(text = '',suts = true){
        text = text ? text : '<?php echo $reallyPrice;?>';
        var res = copyToClip(text);
        if(!res){
            layer.msg('复制失败，请手动复制',{time: 1500,icon: 2});
        }else if(suts){
           layer.msg(/-?\d+(\.\d+)?$/.test(text) ? '复制金额成功，请在输入金额粘贴' : '复制成功',{time: 1500,icon: 1});
        }
    }

    <?php if($pay_type_ali && $zf_pid && $transfer && $status == 0 && request()->isMobile()){?>
    // alipays://platformapi/startapp?appId=20000067&url=
    // https://ds.alipay.com/?scheme=
    // https://render.alipay.com/p/s/i?scheme=
        <?php if($confirm == 1){?>
        $("#alipay").attr('href','alipays://platformapi/startapp?appId=20000067&url=<?php echo urlencode($http_type.$_SERVER['HTTP_HOST'].'/index/pay/goAlipay?trade_no='.$order_id);?>');
        <?php }else{?>
        $("#alipay").attr('href','alipays://platformapi/startapp?appId=20000067&url=<?php echo urlencode($aliTransferUrl ?? '');?>');
        <?php }?>
    <?php }else if($pay_type_ali && (!$zf_pid || !$transfer) && $status == 0 && request()->isMobile()){?>
    $('#alipay').on('click',function() {
        copyText();
    })
    $("#alipay").attr('href','alipays://platformapi/startapp?appId=20000067&url=<?php echo urlencode($payUrl);?>');
    <?php }?>
    <?php if($status == 0 && request()->isMobile()){?>
    $('.picture').on('click',function(){
        copyText();
        savePicture($('#show-qrcode').attr('src'));
    })
    <?php }?>
    <?php if($status == 0 && request()->isMobile()){?>
    var triggerEvent = "touchstart";
    function savePicture(Url){
        var blob=new Blob([''], {type:'application/octet-stream'});
        var url = URL.createObjectURL(blob);
        var a = document.createElement('a');
        a.href = Url;
        a.download = Url.replace(/(.*\/)*([^.]+.*)/ig,"$2").split("?")[0];
        var e = document.createEvent('MouseEvents');
        e.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        a.dispatchEvent(e);
        URL.revokeObjectURL(url);
    }
    <?php }?>
    <?php if(request()->isMobile()){?>
        $('.open_app').show();
    <?php }?>
    
    $('#submitBd').on('click',function(){
        layer.prompt({title: '请输入你的邮箱，不填输入0', formType: 3}, function(email, index){
            layer.close(index);
            // console.log(email);
            layer.load(3);
            $.post('/submitBd',{payId: '<?php echo $order_id;?>',email: email},function(res){
                if(res.code == 1){
                    layer.msg("提交补单成功，等待核实中",{time: 1500,icon: 1});
                }else{
                    layer.msg(res.msg,{time: 1500,icon: 2});
                }
                layer.closeAll('loading');
            })
        });
    })
    $('.copy-text').on('click',function(){
        copyText($(this).attr("copy"));
    })
    <?php if($status == 0 && stripos($ua,'alipay') === false && $voice == 1){?>
        var text = '<?php echo str_replace('{money}',$reallyPrice,get_setting('voice_text'));?>';
        if(text){
            var source = '<?php echo get_setting('voice_text_api');?>'.replace('{text}',text);
            if(source){
                $('body').append('<audio autoplay><source src="'+source+'" type="audio/mpeg">您的浏览器不支持 audio 元素。</audio>');
            }
        }
    <?php }?>
    
    /**
     * 复制单行内容到粘贴板
     * content : 需要复制的内容
     */
    function copyToClip(content) {
        var res = true;
        var aux = document.createElement("input");
        aux.setAttribute("value", content);
        document.body.appendChild(aux);
        aux.select();
        if (!document.execCommand("copy")) {
            res = false;
        }
        document.body.removeChild(aux);
        return res;
    }

    /**
     * 跳转到企业微信客服
     */
    function jumpToWechatWork() {
        <?php if(get_setting('wechat_work_enabled') == 1) { ?>
            // 复制金额
            copyToClip('<?php echo $reallyPrice;?>');
            layer.msg('金额已复制到剪贴板', {icon: 1, time: 2000});
            
            // 企业微信客服已启用，生成客服链接并自动发送消息
            generateCustomerServiceLink();
        <?php } else { ?>
            // 企业微信客服未启用，跳转到原微信链接
            window.location.href = 'weixin://';
        <?php } ?>
    }

    /**
     * 生成企业微信客服链接并自动发送支付消息
     */
    function generateCustomerServiceLink() {
        // 显示加载提示
        var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

        // 获取订单信息
        var tradeNo = '<?php echo isset($order_id) ? $order_id : ""; ?>';
        var amount = '<?php echo isset($reallyPrice) ? $reallyPrice : ""; ?>';
        var productName = '<?php echo isset($goodsname) ? $goodsname : "商品支付"; ?>';

        if (!tradeNo || !amount) {
            layer.close(loadingIndex);
            layer.msg('订单信息不完整', {icon: 2});
            return;
        }

        // 调用API生成客服链接
        $.ajax({
            url: '/wechatwork/generateLink',
            type: 'POST',
            data: {
                trade_no: tradeNo,
                amount: amount,
                product_name: productName
            },
            success: function(response) {
                layer.close(loadingIndex);

                if (response.code === 1) {
                    // 显示客服链接和说明
                    showCustomerServiceModal(response.data);
                } else {
                    layer.msg(response.msg || '生成客服链接失败', {icon: 2});
                }
            },
            error: function() {
                layer.close(loadingIndex);
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
    }

    /**
     * 显示企业微信客服链接弹窗
     */
    function showCustomerServiceModal(data) {
        var modal = layer.open({
            type: 1,
            title: '企业微信客服支付',
            area: ['450px', '400px'],
            content: `
                <div style="padding: 25px;">
                    <div style="text-align: center; margin-bottom: 25px;">
                        <div style="width: 60px; height: 60px; background: #07c160; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; color: white; font-size: 24px;">
                            <i class="fab fa-weixin"></i>
                        </div>
                        <h3 style="margin: 0; color: #333;">自动发送支付消息</h3>
                        <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">点击下方按钮，系统将自动为您发送支付信息</p>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                        <div style="display: flex; align-items: center; margin-bottom: 12px;">
                            <div style="width: 20px; height: 20px; background: #07c160; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-size: 12px; font-weight: bold;">1</div>
                            <span style="color: #333; font-weight: 500;">点击"进入客服"打开企业微信</span>
                        </div>
                        <div style="display: flex; align-items: center; margin-bottom: 12px;">
                            <div style="width: 20px; height: 20px; background: #07c160; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-size: 12px; font-weight: bold;">2</div>
                            <span style="color: #333; font-weight: 500;">系统自动发送支付信息和二维码</span>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div style="width: 20px; height: 20px; background: #07c160; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-size: 12px; font-weight: bold;">3</div>
                            <span style="color: #333; font-weight: 500;">长按二维码完成支付</span>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button onclick="openCustomerService('${data.customer_service_url}', '${data.session_id}')" style="background: linear-gradient(135deg, #07c160 0%, #00ae56 100%); color: white; border: none; padding: 14px 35px; border-radius: 25px; font-size: 16px; font-weight: 600; cursor: pointer; margin-right: 15px;">
                            <i class="fab fa-weixin"></i> 进入客服
                        </button>
                        <button onclick="layer.close(${modal})" style="background: #f5f5f5; color: #666; border: none; padding: 14px 25px; border-radius: 25px; font-size: 16px; cursor: pointer;">
                            取消
                        </button>
                    </div>
                </div>
            `
        });
    }

    /**
     * 打开企业微信客服
     */
    function openCustomerService(customerServiceUrl, sessionId) {
        // 打开企业微信客服链接
        window.open(customerServiceUrl, '_blank');

        // 延迟触发自动发送消息
        setTimeout(function() {
            triggerAutoSendMessage(sessionId);
        }, 2000);

        // 显示成功提示
        layer.msg('正在打开企业微信客服，系统将自动发送支付信息', {icon: 1, time: 4000});

        // 关闭弹窗
        layer.closeAll();
    }

    /**
     * 触发自动发送消息
     */
    function triggerAutoSendMessage(sessionId) {
        $.ajax({
            url: '/wechatwork/handleAccess',
            type: 'POST',
            data: {
                session_id: sessionId
            },
            success: function(response) {
                if (response.code === 1) {
                    console.log('支付消息已自动发送');
                } else {
                    console.log('自动发送消息失败: ' + response.msg);
                }
            },
            error: function() {
                console.log('触发自动发送消息失败');
            }
        });
    }

    // 企业微信客服跳转class绑定
    $(document).on('click', '.wechat-work-jump', function(e) {
        e.preventDefault();
        jumpToWechatWork();
    });
    </script>
