<?php
require_once 'app/common.php';
require_once 'extend/callback/XMLParse.php';
require_once 'extend/callback/ErrorCode.php';

use callback\XMLParse;
use callback\ErrorCode;

echo "=== XML 解析测试 ===\n\n";

// 从日志中提取的加密消息
$encryptedMessages = [
    [
        'xml' => '<xml><ToUserName><![CDATA[ww666a49]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>**********</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[update_user]]></ChangeType><UserID><![CDATA[HuangJianPing]]></UserID><NewUserID><![CDATA[HuangJianPing]]></NewUserID><Name><![CDATA[黄建平]]></Name><Department><![CDATA[1]]></Department><IsLeaderInDept><![CDATA[0]]></IsLeaderInDept><Position><![CDATA[测试账号]]></Position><Mobile><![CDATA[13800138000]]></Mobile><Gender><![CDATA[1]]></Gender><Email><![CDATA[<EMAIL>]]></Email><Status><![CDATA[1]]></Status><Avatar><![CDATA[http://wework.qpic.cn/bizmail/xxx]]></Avatar><Alias><![CDATA[HuangJianPing]]></Alias><OpenUserID><![CDATA[woHuangJianPing]]></OpenUserID><MainDepartment><![CDATA[1]]></MainDepartment><ExtAttr><Item><Name><![CDATA[爱好]]></Name><Value><![CDATA[旅游]]></Value></Item></ExtAttr></xml>',
        'encrypt' => '06g5pkwviNIxd2DM0P2l+9JEQPF5p7zy...',
        'msg_signature' => 'test_signature_1',
        'timestamp' => '**********',
        'nonce' => '12345678'
    ]
];

$xmlparse = new XMLParse();

foreach ($encryptedMessages as $index => $message) {
    echo "--- 测试消息 " . ($index + 1) . " ---\n";
    
    // 测试 XML 解析
    echo "1. 测试 XML 解析:\n";
    echo "   XML 内容: " . substr($message['xml'], 0, 100) . "...\n";
    
    $result = $xmlparse->extract($message['xml']);
    echo "   解析结果: " . $result[0] . "\n";
    
    if ($result[0] === 0) {
        echo "   ✅ XML 解析成功\n";
        echo "   提取的加密内容: " . substr($result[1], 0, 50) . "...\n";
    } else {
        echo "   ❌ XML 解析失败\n";
        echo "   错误码: " . $result[0] . "\n";
        
        // 检查 XML 格式
        echo "   检查 XML 格式:\n";
        $xml = new DOMDocument();
        libxml_use_internal_errors(true);
        $loadResult = $xml->loadXML($message['xml']);
        if (!$loadResult) {
            echo "   ❌ XML 格式错误\n";
            $errors = libxml_get_errors();
            foreach ($errors as $error) {
                echo "   错误: " . $error->message . "\n";
            }
            libxml_clear_errors();
        } else {
            echo "   ✅ XML 格式正确\n";
            
            // 检查 Encrypt 节点
            $encryptNodes = $xml->getElementsByTagName('Encrypt');
            if ($encryptNodes->length > 0) {
                echo "   ✅ 找到 Encrypt 节点\n";
                $encryptNode = $encryptNodes->item(0);
                echo "   Encrypt 节点值: " . substr($encryptNode->nodeValue, 0, 50) . "...\n";
            } else {
                echo "   ❌ 未找到 Encrypt 节点\n";
                
                // 列出所有节点
                echo "   XML 中的所有节点:\n";
                $allNodes = $xml->getElementsByTagName('*');
                foreach ($allNodes as $node) {
                    echo "   - " . $node->nodeName . "\n";
                }
            }
        }
    }
    
    echo "\n";
}

// 测试生成 XML
echo "2. 测试生成 XML:\n";
$testEncrypt = "test_encrypt_content";
$testSignature = "test_signature";
$testTimestamp = time();
$testNonce = "test_nonce";

$generatedXml = $xmlparse->generate($testEncrypt, $testSignature, $testTimestamp, $testNonce);
echo "生成的 XML:\n";
echo $generatedXml . "\n\n";

// 测试解析生成的 XML
echo "3. 测试解析生成的 XML:\n";
$result = $xmlparse->extract($generatedXml);
echo "解析结果: " . $result[0] . "\n";
if ($result[0] === 0) {
    echo "✅ 解析成功\n";
    echo "提取的加密内容: " . $result[1] . "\n";
} else {
    echo "❌ 解析失败\n";
}

echo "\n=== 测试完成 ===\n";