<?php

namespace app\index\controller;

use app\common\controller\CommonController;
use app\common\service\EPayService;

class Index extends CommonController
{
    public function __construct()
	{
	    $authcode = get_setting('authcode');
        if(!$authcode){
            $this->error('请填写授权码');
        }
        $zz_id = '2';
        $zz_url = 'auth.zzwws.cn';
        $root = $_SERVER['DOCUMENT_ROOT'];
        $host = $_SERVER['HTTP_HOST'];
        $server_ip = !empty($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : $_SERVER['APP_POOL_ID'];
        $date = date("Y-m-d");
        $zz_auth = cache('zz_auth_'.$server_ip);
        if(md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM') !== $zz_auth){
        $zz_query=get_curl('http://'.$zz_url.'/ZR/api/zr_check.php?url='.$host.'&authcode='.$authcode.'&version=&ZRid='.$zz_id);
            if ($zz_query = json_decode($zz_query, true)) {
        		if ($zz_query["code"] == 0) {
        			cache('zz_auth_'.$server_ip,md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM'),3600*24);
        		}else{
        			$this->error($zz_query['msg']);
        		}
        	}else{
        	    $this->error('查询授权信息失败');
        	}
        }
	}
	
    /**
     * 前台页面
     */
    public function index($type = 'index')
    {
        $home_url = get_setting('home_url');
        if($home_url){
            header('location: '.$home_url);
        }

        $data = [
            'title' => get_setting('title'),
            'keywords' => get_setting('keywords'),
            'description' => get_setting('description'),
            'logo' => get_setting('logo_image'),
            'footer' => get_setting('footer'),
            'edition' => file_get_contents(ROOT_PATH.'version'),
        ];
        return view(root_path() . 'view' . DS . 'index' . DS . $type.'.html',$data);
    }
    
    /**
     * 支付测试
     */
    public function demo()
    {
        $param = input('param.');
        $epay = new EPayService();
        $param['pid'] = get_setting('epayid_ali');
        $param['notify_url'] = 'http://'.$_SERVER['HTTP_HOST'].'/index/index/demoNotifyUrl';
        $param['return_url'] = 'http://'.$_SERVER['HTTP_HOST'].'/index/index/demoReturnUrl';
        $param['name'] = get_setting('title').'测试商品';
        $param['sitename'] = get_setting('title');
        echo $epay->buildRequestForm($param);
        // echo $epay->buildRequestParaToString($param);
    }

    /**
     * 支付测试异步回调
     */
    public function demoNotifyUrl()
    {
        $param = input('param.');
        if(empty($param['sign'])){
            $this->error('签名不能为空');
        }
        $epay = new EPayService();
        if($epay->getSignVeryfy($param,$param['sign'])){
            exit('success');
        }else{
            exit('fail');
        }
    }

    /**
     * 支付测试同步回调
     */
    public function demoReturnUrl()
    {
        $param = input('param.');
        if(empty($param['sign'])){
            $this->error('签名不能为空');
        }
        $epay = new EPayService();
        if($epay->getSignVeryfy($param,$param['sign'])){
            echo '验证成功';
        }else{
            echo '验证失败';
        }
    }

    /**
     * 获取支付方式API
     */
    public function paymentMethods()
    {
        // 设置跨域头
        header('Access-Control-Allow-Origin: *');
        header('Content-Type: application/json');

        // 支付方式配置
        $paymentMethods = [
            [
                'type' => 'wxpay',
                'name' => '微信支付',
                'icon' => '/static/web/picture/wx_pay.svg',
                'enabled' => true
            ],
            [
                'type' => 'alipay',
                'name' => '支付宝',
                'icon' => '/static/web/picture/alipay.svg',
                'enabled' => true
            ],
            [
                'type' => 'qqpay',
                'name' => 'QQ支付',
                'icon' => '/static/web/picture/qqpay.png',
                'enabled' => true
            ],
            [
                'type' => 'rmbpay',
                'name' => '数字人民币',
                'icon' => '/static/web/picture/rmbpay.png',
                'enabled' => true
            ],
            [
                'type' => 'usdtpay',
                'name' => 'USDT支付',
                'icon' => '/static/web/picture/usdt.png',
                'enabled' => true
            ]
        ];

        // 过滤启用的支付方式
        $enabledMethods = array_filter($paymentMethods, function($method) {
            return $method['enabled'];
        });

        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => array_values($enabledMethods)
        ]);
    }

    /**
     * 生成二维码
     */
    public function enQrcode($url)
    {
        if (!$url) {
            $this->error('url参数不能为空');
        }
        if(stripos($url,'scheme=') !== false){
            $url1 = substr($url,0,stripos($url,'scheme=')+7);
            $url2 = substr($url,stripos($url,'scheme=')+7);
            $url = $url1.urlencode($url2);
            // 返回处理后的URL
        }
        $qr_code = new \app\common\service\QrcodeService();
        $content = $qr_code->createServer($url);
        return response($content, 200, ['Content-Length' => strlen($content)])->contentType('image/png');
    }
}
