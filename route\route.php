<?php
use think\facade\Route;

Route::any('/enQrcode','app\index\controller\Index@enQrcode');
Route::any('/checkOrder','app\index\controller\Pay@checkOrder');
Route::any('/appPush','app\index\controller\Pay@appPush');
Route::any('/appHeart','app\index\controller\Pay@appHeart');
Route::any('/submitBd','app\index\controller\Pay@submitBd');
Route::any('/pay/apiSubmit','app\index\controller\Pay@apiSubmit');
Route::any('/pay/chaorder','app\index\controller\Pay@chaorder');
Route::any('/appPast','app\index\controller\Pay@appPast');

// 企业微信客服路由
Route::group('wechatwork', function () {
    Route::any('/', 'app\index\controller\WechatWork@index');
    Route::any('/notify', 'app\index\controller\WechatWork@notify');
    Route::any('/return', 'app\index\controller\WechatWork@returnPage');
    Route::any('/config', 'app\index\controller\WechatWork@config');
    Route::any('/testConnection', 'app\index\controller\WechatWork@testConnection');
    Route::any('/getStats', 'app\index\controller\WechatWork@getStats');
    Route::any('/generateLink', 'app\index\controller\WechatWork@generateCustomerServiceLink');
    Route::any('/handleAccess', 'app\index\controller\WechatWork@handleServiceAccess');
    Route::any('/diagnose', 'app\index\controller\WechatWork@diagnose');
    Route::any('/oauthCallback', 'app\index\controller\WechatWork@oauthCallback');
});

Route::any('/api','app\index\controller\Index@index')->append(['type' => 'api']);
Route::any('/demo','app\index\controller\Index@index')->append(['type' => 'demo']);
Route::any('/doc','app\index\controller\Index@index')->append(['type' => 'doc']);
Route::any('/index1','app\index\controller\Index@index')->append(['type' => 'index1']);
Route::any('/result','app\index\controller\Index@index')->append(['type' => 'result']);
Route::any('/chaorder','app\index\controller\Index@index')->append(['type' => 'chaorder']);