<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>订单管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="__STATIC__/lib/layui-v2.11.4/css/layui.css" media="all">
    <link rel="stylesheet" href="__STATIC__/css/public.css" media="all">
    <link rel="stylesheet" href="__STATIC__/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">

        <fieldset class="table-search-fieldset">
            <legend>搜索信息</legend>
            <div style="margin: 10px 10px 10px 10px">
                <form class="layui-form layui-form-pane" action="">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">时间范围</label>
                            <div class="layui-input-inline">
                                <input type="text" id="time" name="time" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">支付模式</label>
                            <div class="layui-input-inline">
                                <select name="type" lay-search="">
                                    <option value="">直接选择或搜索选择</option>
                                    {foreach $payType as $k => $v}
                                    <option value="{$k}">{$v}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">通道模式</label>
                            <div class="layui-input-inline">
                                <select name="channel_mode" lay-search="">
                                    <option value="">直接选择或搜索选择</option>
                                    {foreach $payCode as $k => $v}
                                    <option value="{$k}">{$v}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">订单号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="trade_no" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-inline">
                                <select name="status" lay-search="">
                                    <option value="">直接选择或搜索选择</option>
                                    <option value="0">未支付</option>
                                    <option value="1">已支付</option>
                                    <option value="2">回调失败</option>
                                    <option value="3">已过期</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn layui-btn-primary" lay-submit lay-filter="data-search-btn"><i class="layui-icon"></i> 搜 索</button>
                        </div>
                    </div>
                </form>
            </div>
        </fieldset>
        <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
    </div>
</div>
<!-- 工具栏 -->
<script type="text/html" id="toolbarDemo">
  <button class="layui-btn layui-btn-sm layuimini-btn-primary" lay-event="reload"><i class="fa fa-refresh"></i></button>
<button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="delAll"><i class="fa fa-trash-o"></i> 批量删除</button>
</script>
<!-- 工具 -->
<script type="text/html" id="barDemo">
  <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="setBd">补单</a>
  <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>
<script type="text/html" id="status">
  {{# if(d.status == 0){ }}
  <a class="layui-btn layui-btn-primary layui-btn-xs">待支付</a>
  {{# }else if(d.status == 1){ }}
  <a class="layui-btn layui-btn-success layui-btn-xs">已支付</a>
  {{# }else if(d.status == 2){ }}
  <a class="layui-btn layui-btn-warm layui-btn-xs">回调失败</a>
  {{# }else if(d.status == 3){ }}
  <a class="layui-btn layui-btn-danger layui-btn-xs">已过期</a>
  {{# } }}
</script>
<script src="__STATIC__/lib/layui-v2.11.4/layui.js" charset="utf-8"></script>
<script>
    layui.use(['form', 'table','laydate'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table,
            laydate = layui.laydate;

        //日期时间范围选择
        laydate.render({ 
          elem: '#time'
          ,type: 'datetime'
          ,range: true
        });

        table.render({
            elem: '#currentTableId'
            ,url:'{:url("system.order/index")}'
            ,method:'post'
            ,toolbar:'#toolbarDemo'
            ,defaultToolbar: false //去掉右侧工具栏
            // ,cellMinWidth: 100
            ,totalRow: true
            ,cols: [[
              {type:'checkbox'}
              ,{width:80,field:'id',title:'ID',sort: true,align:'center',totalRow:'合计：'}
              ,{width:80,field:'pid',title:'通道ID',align:'center'}
              //,{width:150,field:'type',title:'支付方式',align:'center'}
              ,{width:150,field:'channel_mode',title:'通道模式',align:'center'}
              ,{width:100,field:'money',title:'金额',align:'center',totalRow:'{{= d.TOTAL_NUMS }}'}
              ,{width:100,field:'truemoney',title:'实付金额',align:'center',totalRow:'{{= d.TOTAL_NUMS }}'}
              ,{width:80,field:'status',title:'状态',templet:'#status',sort:true,align:'center'}
              ,{width:180,field:'trade_no',title:'订单号',align:'center'}
              ,{width:180,field:'out_trade_no',title:'商户订单号',align:'center'}
              ,{width:180,field:'receive_order',title:'收款平台订单号',align:'center'}
              ,{width:180,field:'receive_memo',title:'收款平台备注',align:'center'}
              ,{width:180,field:'name',title:'商品名称',align:'center'}
              ,{width:180,field:'sitename',title:'网站名称',align:'center'}
              ,{width:180,field:'email',title:'补单邮箱',align:'center'}
              ,{width:160,field:'create_time',title:'添加时间',sort:true,align:'center'}
              ,{width:160,field:'pay_time',title:'支付时间',sort:true,align:'center'}
              ,{width:160,title:'操作',toolbar:'#barDemo',align:'center',fixed:'right'}
            ]]
            ,page: true
            ,limits: [20, 50, 100]
            ,limit: 20
            ,skin: 'line'
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            // console.log(data.field);

            //执行搜索重载
            table.reload('currentTableId', {
                page: {
                    curr: 1
                }
                , where: {
                    search: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar(currentTableFilter)', function (obj) {
            if (obj.event === 'delAll') {  // 监听删除操作
                var checkStatus = table.checkStatus('currentTableId')
                    , data = checkStatus.data;
                // console.log(data);
                if(data == ''){
                    layer.msg('未选中需要删除的订单',{time: 1500,icon: 2});
                }else{
                    layer.confirm('是否批量删除订单', {
                        btn: ['是的', '取消']
                    }, function() {
                        ids = [];
                        $.each(data, function(i, v) {
                            ids.push(v['id']);
                        })
                        $.post('{:url("system.order/del")}', { id: ids }, function(res) {
                            // console.log(res);
                            var icon = res.code == 0 ? 2 : 1;
                            layer.msg(res.msg, { time: 1500, icon: icon }, function() {
                                if (res.code == 1) {
                                    location.reload();
                                }
                            })
                        })
                    })
                }
            }else if(obj.event == 'reload'){
                location.reload();
            }
        });

        //监听表格复选框选择
        /*table.on('checkbox(currentTableFilter)', function (obj) {
            console.log(obj)
        });*/

        table.on('tool(currentTableFilter)', function (obj) {
            var data = obj.data;
            // console.log(data);
            if (obj.event === 'del') {
                layer.confirm('真的删除这行么', function (index) {
                    $.post('{:url("system.order/del")}',{id: data.id},function(res){
                        icon = res.code == 1 ? 1 : 2;
                        layer.msg(res.msg,{time: 1500,icon: icon},function(){
                            if(res.code == 1){
                                location.reload();
                            }
                        })
                    })
                });
            }else if(obj.event == 'setBd'){
                layer.confirm('确定要补单吗？该操作将会将该订单标记为已支付，并向您的服务器发送订单完成通知', function (index) {
                    layer.load(3);
                    $.post('{:url("system.order/setBd")}',{id: data.id},function(res){
                        layer.closeAll('loading');
                        if(res.code == 1){
                            layer.msg(res.msg,{time: 1500,icon: 1},function(){
                                location.reload();
                            });
                        }else{
                            layer.confirm('补单失败，异步通知返回错误，是否查看通知返回数据？', {
                                btn: ['查看','打开链接','取消'] //按钮
                            }, function(){
                                layer.alert(res.data.ret);
                            }, function(){
                                open(res.data.url);
                            }, function(){

                            });
                        }
                    })
                });
            }
        });
    });
</script>

</body>
</html>