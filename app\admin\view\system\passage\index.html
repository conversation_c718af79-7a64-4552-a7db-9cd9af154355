<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>通道管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="__STATIC__/lib/layui-v2.11.4/css/layui.css" media="all">
    <link rel="stylesheet" href="__STATIC__/css/public.css" media="all">
    <link rel="stylesheet" href="__STATIC__/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <style>
        /* 自定义按钮样式 */
        .layui-btn-warm {
            background-color: #ff9800;
            border-color: #ff9800;
            color: #fff;
        }
        .layui-btn-warm:hover {
            background-color: #f57c00;
            border-color: #f57c00;
        }

        /* APP配置弹窗样式 */
        .app-config-content {
            padding: 20px;
            text-align: center;
        }
        .app-config-title {
            margin-bottom: 15px;
            font-size: 16px;
            color: #333;
            font-weight: 500;
        }
        .app-config-qrcode {
            width: 200px;
            height: 200px;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .app-config-tip {
            margin-top: 15px;
            color: #666;
            font-size: 12px;
        }
        .app-config-textarea {
            width: 100%;
            height: 60px;
            border: 1px solid #ddd;
            background: #fff;
            resize: none;
            font-family: 'Courier New', monospace;
            padding: 10px;
            border-radius: 4px;
            font-size: 13px;
            line-height: 1.4;
        }
        .app-config-textarea:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        /* 配置方式分隔样式 */
        .config-method-title {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
            font-weight: 500;
            padding-bottom: 8px;
            border-bottom: 2px solid;
        }
        .config-method-title.qrcode {
            border-bottom-color: #1890ff;
        }
        .config-method-title.manual {
            border-bottom-color: #ff9800;
        }

        /* 配置区域样式 */
        .config-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            margin-bottom: 20px;
        }

        /* 说明区域样式 */
        .config-tips {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
            margin-top: 20px;
        }
    </style>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">

        <fieldset class="table-search-fieldset">
            <legend>搜索信息</legend>
            <div style="margin: 10px 10px 10px 10px">
                <form class="layui-form layui-form-pane" action="">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">时间范围</label>
                            <div class="layui-input-inline">
                                <input type="text" id="time" name="time" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">支付模式</label>
                            <div class="layui-input-inline">
                                <select name="type" lay-search="">
                                    <option value="">直接选择或搜索选择</option>
                                    {foreach $payType as $k => $v}
                                    <option value="{$k}">{$v}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">通道模式</label>
                            <div class="layui-input-inline">
                                <select name="code" lay-search="">
                                    <option value="">直接选择或搜索选择</option>
                                    {foreach $payCode as $k => $v}
                                    <option value="{$k}">{$v}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">支付用户ID</label>
                            <div class="layui-input-inline">
                                <input type="text" name="zf_pid" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">在线状态</label>
                            <div class="layui-input-inline">
                                <select name="is_status">
                                    <option value=""></option>
                                    <option value="0">离线</option>
                                    <option value="1">在线</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-inline">
                                <select name="status">
                                    <option value=""></option>
                                    <option value="0">禁用</option>
                                    <option value="1">开启</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">备注</label>
                            <div class="layui-input-inline">
                                <input type="text" name="remarks" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn layui-btn-primary" lay-submit lay-filter="data-search-btn"><i class="layui-icon"></i> 搜 索</button>
                        </div>
                    </div>
                </form>
            </div>
        </fieldset>
        <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>
    </div>
</div>
<!-- 工具栏 -->
<script type="text/html" id="toolbarDemo">
  <button class="layui-btn layui-btn-sm layuimini-btn-primary" lay-event="reload"><i class="fa fa-refresh"></i></button>
<button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="add"><i class="fa fa-plus"></i> 添加</button>
<button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="delAll"><i class="fa fa-trash-o"></i> 批量删除</button>
<button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="qrcode"><i class="fa fa-plus"></i> 有金额收款码</button>
</script>
<!-- 工具 -->
<script type="text/html" id="barDemo">
  <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="appConfig">配置</a>
  <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit">编辑</a>
  <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>
<script type="text/html" id="status">
  {{# if(d.id == 1){ }}
  <input type="checkbox" name="" value="{{d.id}}" lay-skin="switch" lay-text="开启" lay-filter="status" disabled checked>
  {{# }else{ }}
  <input type="checkbox" name="status" value="{{d.id}}" lay-skin="switch" lay-text="开启|禁用" lay-filter="status" {{ d.status == 1 ? 'checked' : '' }}>
  {{# } }}
</script>
<script type="text/html" id="is_status">
    {{# if(d.is_status == 1){ }}
        <a class="layui-btn layui-btn-success layui-btn-xs">在线</a>
    {{# }else{ }}
        <a class="layui-btn layui-btn-danger layui-btn-xs">离线</a>
    {{# } }}
  </script>
<script src="__STATIC__/lib/layui-v2.11.4/layui.js" charset="utf-8"></script>
<script src="__STATIC__/js/common.js"></script>
<script>
    layui.use(['form', 'table','laydate'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table,
            laydate = layui.laydate;

        //日期时间范围选择
        laydate.render({ 
          elem: '#time'
          ,type: 'datetime'
          ,range: true
        });

        table.render({
            elem: '#currentTableId'
            ,url:'{:url("system.passage/index")}'
            ,method:'post'
            ,toolbar:'#toolbarDemo'
            ,defaultToolbar: false //去掉右侧工具栏
            // ,cellMinWidth: 100
            ,cols: [[
              {type:'checkbox'}
              ,{width:100,field:'id',title:'ID',sort: true,align:'center'}
              ,{width:200,field:'type',title:'支付模式',align:'center'}
              ,{width:200,field:'code',title:'通道模式',align:'center'}
              ,{width:100,field:'is_status',title:'在线状态',align:'center',templet:'#is_status'}
              ,{width:100,field:'status',title:'状态',templet:'#status',sort:true,align:'center'}
              ,{width:100,field:'balance',title:'当前余额',align:'center'}
              ,{width:100,field:'succcount',title:'收款笔数',align:'center'}
              ,{width:100,field:'succprice',title:'收款金额',align:'center'}
              ,{width:120,field:'single_limit',title:'单笔限额',align:'center',templet:function(d){
                return d.single_limit > 0 ? d.single_limit : '不限制';
              }}
              ,{width:120,field:'daily_limit',title:'单日限额',align:'center',templet:function(d){
                return d.daily_limit > 0 ? d.daily_limit : '不限制';
              }}
              ,{width:120,field:'today_amount',title:'今日已收',align:'center',templet:function(d){
                var amount = d.today_amount || 0;
                var limit = d.daily_limit || 0;
                if(limit > 0 && amount >= limit){
                  return '<span style="color:red;">' + amount + '</span>';
                }
                return amount;
              }}
              ,{width:200,field:'zf_pid',title:'支付用户ID',align:'center'}
              ,{width:120,field:'ewm_type',title:'收款码类型',align:'center',templet:function(d){
                if(d.ewm_type == 0){
                    return '二维码';
                }else{
                    return '赞赏码';
                }
              }}
              ,{width:120,field:'ewm',title:'无金额收款码',align:'center',templet:function(d){
                if(d.ewm){
                    return '<button type="button" class="layui-btn layui-btn-xs" src="/enQrcode?url='+d.ewm+'" onclick="showBigImage(this,210,210);">查看</button>';
                }else{
                    return '';
                }
              }}
              ,{width:200,field:'create_time',title:'添加时间',sort:true,align:'center'}
              ,{width:200,field:'ck_time',title:'CK更新时间',sort:true,align:'center'}
              ,{width:200,field:'run_time',title:'监控运行时间',sort:true,align:'center'}
              ,{width:200,field:'remarks',title:'备注',align:'center'}
              ,{width:250,title:'操作',toolbar:'#barDemo',align:'center',fixed:'right'}
            ]]
            ,page: true
            ,limits: [20, 50, 100]
            ,limit: 20
            ,skin: 'line'
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            // console.log(data.field);

            //执行搜索重载
            table.reload('currentTableId', {
                page: {
                    curr: 1
                }
                , where: {
                    search: data.field
                }
            }, 'data');

            return false;
        });

        /**
         * toolbar监听事件
         */
        table.on('toolbar(currentTableFilter)', function (obj) {
            if (obj.event === 'add') {  // 监听添加操作
                var index = layer.open({
                    title: '添加通道',
                    type: 2,
                    shade: 0.2,
                    maxmin:true,
                    shadeClose: true,
                    area: ['65%', '85%'],
                    content: '{:url("system.passage/add")}',
                });
                $(window).on("resize", function () {
                    layer.full(index);
                });
            } else if (obj.event === 'delAll') {  // 监听删除操作
                var checkStatus = table.checkStatus('currentTableId')
                    , data = checkStatus.data;
                // console.log(data);
                if(data == ''){
                    layer.msg('未选中需要删除的通道',{time: 1500,icon: 2});
                }else{
                    layer.confirm('是否批量删除通道', {
                        btn: ['是的', '取消']
                    }, function() {
                        ids = [];
                        $.each(data, function(i, v) {
                            ids.push(v['id']);
                        })
                        $.post('{:url("system.passage/del")}', { id: ids }, function(res) {
                            // console.log(res);
                            var icon = res.code == 0 ? 2 : 1;
                            layer.msg(res.msg, { time: 1500, icon: icon }, function() {
                                if (res.code == 1) {
                                    location.reload();
                                }
                            })
                        })
                    })
                }
            }else if(obj.event == 'reload'){
                location.reload();
            }else if(obj.event == 'qrcode'){
                var index = layer.open({
                    title: '有金额收款码',
                    type: 2,
                    shade: 0.2,
                    maxmin:true,
                    shadeClose: true,
                    area: ['65%', '85%'],
                    content: '{:url("system.qrcode/index")}',
                });
                $(window).on("resize", function () {
                    layer.full(index);
                });
            }
        });

        //监听表格复选框选择
        /*table.on('checkbox(currentTableFilter)', function (obj) {
            console.log(obj)
        });*/

        table.on('tool(currentTableFilter)', function (obj) {
            var data = obj.data;
            // console.log(data);
            if (obj.event === 'edit') {
                var index = layer.open({
                    title: '编辑通道',
                    type: 2,
                    shade: 0.2,
                    maxmin:true,
                    shadeClose: true,
                    area: ['65%', '85%'],
                    content: '{:url("system.passage/edit")}?id='+data.id,
                });
                $(window).on("resize", function () {
                    layer.full(index);
                });
            } else if (obj.event === 'del') {
                layer.confirm('真的删除这行么', function (index) {
                    $.post('{:url("system.passage/del")}',{id: data.id},function(res){
                        icon = res.code == 1 ? 1 : 2;
                        layer.msg(res.msg,{time: 1500,icon: icon},function(){
                            if(res.code == 1){
                                location.reload();
                            }
                        })
                    })
                });
            }else if(obj.event == 'ewm'){
                var index = layer.open({
                    title: '查看二维码',
                    type: 2,
                    shade: 0.2,
                    maxmin:true,
                    shadeClose: true,
                    area: ['300px', '300px'],
                    content: '{:url("system.passage/ewm")}?url='+data.ewm,
                });
                $(window).on("resize", function () {
                    layer.full(index);
                });
            }else if(obj.event == 'appConfig'){
                // APP配置（扫码+手动配置）
                var configText = '{$appConfig}'+data.id+','+data.old_type;
                var qrcodeUrl = '/enQrcode?url=' + encodeURIComponent(configText);
                
                // 判断支付模式是否为QQ
                var isQQPay = data.type && (data.type.indexOf('QQ') !== -1 || data.type.indexOf('qq') !== -1);
                
                // 构建配置内容
                var configContent = '<div style="padding:25px;">';
                
                // 如果不是QQ支付，显示扫码配置
                if(!isQQPay) {
                    configContent +=
                            // 扫码配置部分
                            '<div style="margin-bottom:30px;">' +
                            '<h3 style="margin:0 0 15px 0;color:#333;font-size:16px;border-bottom:2px solid #1890ff;padding-bottom:8px;">' +
                            '<i class="fa fa-qrcode" style="margin-right:8px;color:#1890ff;"></i>方式一：扫码配置</h3>' +
                            '<div style="text-align:center;background:#f8f9fa;padding:20px;border-radius:8px;border:1px solid #e9ecef;">' +
                            '<p style="margin:0 0 15px 0;color:#666;font-size:14px;">使用APP扫描下方二维码自动配置</p>' +
                            '<img src="' + qrcodeUrl + '" style="width:200px;height:200px;border:1px solid #ddd;border-radius:8px;box-shadow:0 2px 8px rgba(0,0,0,0.1);" />' +
                            '<p style="margin:15px 0 0 0;color:#999;font-size:12px;"><i class="fa fa-info-circle"></i> 扫码后按照APP提示完成配置</p>' +
                            '</div>' +
                            '</div>';
                }
                
                // 手动配置部分（始终显示）
                configContent +=
                            // 手动配置部分
                            '<div>' +
                            '<h3 style="margin:0 0 15px 0;color:#333;font-size:16px;border-bottom:2px solid #ff9800;padding-bottom:8px;">' +
                            '<i class="fa fa-edit" style="margin-right:8px;color:#ff9800;"></i>' + (isQQPay ? '手动配置' : '方式二：手动配置') + '</h3>' +
                            '<div style="background:#f8f9fa;padding:20px;border-radius:8px;border:1px solid #e9ecef;">' +
                            '<p style="margin:0 0 10px 0;color:#666;font-size:14px;">复制以下配置信息到' + (isQQPay ? 'PC' : 'APP/PC') + '中：</p>' +
                            '<div style="position:relative;">' +
                            '<textarea readonly class="app-config-textarea" onclick="this.select()" id="configTextarea" style="height:60px;font-size:13px;">' + configText + '</textarea>' +
                            '<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" onclick="copyToClipboard(\'' + configText + '\')" style="position:absolute;top:5px;right:5px;"><i class="fa fa-copy"></i></button>' +
                            '</div>' +
                            '</div>' +
                            '</div>';

                // 使用说明
                var tipsContent =
                            '<div style="background:#e8f4fd;padding:15px;border-radius:6px;margin-top:20px;border-left:4px solid #1890ff;">' +
                            '<div style="color:#0c5460;font-size:13px;margin-bottom:8px;"><i class="fa fa-lightbulb-o" style="margin-right:5px;"></i><strong>配置说明：</strong></div>' +
                            '<div style="color:#0c5460;font-size:12px;line-height:1.6;">';
                
                if(!isQQPay) {
                    tipsContent += '• <strong>扫码配置</strong>：推荐方式，快速便捷，自动填入配置信息<br/>' +
                                    '• <strong>手动配置</strong>：适用于无法扫码的情况或者PC端，需手动复制粘贴<br/>';
                } else {
                    tipsContent += '• <strong>手动配置</strong>：QQ支付模式仅支持手动配置，请复制以下配置信息到PC中<br/>';
                }
                
                tipsContent += '• 确保监控端和服务器网络连通，配置信息完整无误' +
                            '</div>' +
                            '</div>';
                
                configContent += tipsContent + '</div>';
                
                layer.open({
                    title: '<i class="fa fa-mobile"></i> APP/PC配置',
                    type: 1,
                    area: ['650px', '600px'],
                    content: configContent,
                    btn: ['关闭'],
                    shadeClose: true,
                    yes: function(index) {
                        layer.close(index);
                    }
                });
            }
        });

        // 监听状态事件
        form.on('switch(status)', function(obj) {
            // console.log(obj);
            // layer.tips(this.value + ' ' + this.name + '：'+ obj.elem.checked, obj.othis);
            var status = obj.elem.checked ? 1 : 0;
            $.post('{:url("system.passage/status")}', { status: status, id: this.value }, function(res) {
                // console.log(res);
                if (res.code == 0) {
                    layer.msg(res.msg, { time: 1500, icon: 2 }, function() {
                        location.reload();
                    })
                }
            })
        });
    });

    // 复制到剪贴板功能
    function copyToClipboard(text) {
        if (navigator.clipboard && window.isSecureContext) {
            // 现代浏览器的方法
            navigator.clipboard.writeText(text).then(function() {
                layer.msg('配置信息已复制到剪贴板', {icon: 1});
            }).catch(function(err) {
                console.error('复制失败:', err);
                fallbackCopyTextToClipboard(text);
            });
        } else {
            // 兼容旧浏览器的方法
            fallbackCopyTextToClipboard(text);
        }
    }

    // 兼容旧浏览器的复制方法
    function fallbackCopyTextToClipboard(text) {
        var textArea = document.createElement("textarea");
        textArea.value = text;

        // 避免滚动到底部
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            var successful = document.execCommand('copy');
            if (successful) {
                layer.msg('配置信息已复制到剪贴板', {icon: 1});
            } else {
                layer.msg('复制失败，请手动复制', {icon: 2});
            }
        } catch (err) {
            console.error('复制失败:', err);
            layer.msg('复制失败，请手动复制', {icon: 2});
        }

        document.body.removeChild(textArea);
    }
</script>

</body>
</html>