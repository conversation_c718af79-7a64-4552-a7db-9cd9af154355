<?php
namespace app\admin\controller\system;

use app\common\controller\CommonController;

class Cron extends CommonController
{
    public function __construct()
	{
	    $authcode = get_setting('authcode');
        if(!$authcode){
            $this->error('请填写授权码');
        }
        $zz_id = '2';
        $zz_url = 'auth.zzwws.cn';
        $root = $_SERVER['DOCUMENT_ROOT'];
        $host = $_SERVER['HTTP_HOST'];
        $server_ip = !empty($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : $_SERVER['APP_POOL_ID'];
        $date = date("Y-m-d");
        $zz_auth = cache('zz_auth_'.$server_ip);
        if(md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM') !== $zz_auth){
        $zz_query=get_curl('http://'.$zz_url.'/ZR/api/zr_check.php?url='.$host.'&authcode='.$authcode.'&version=&ZRid='.$zz_id);
            if ($zz_query = json_decode($zz_query, true)) {
        		if ($zz_query["code"] == 0) {
        			cache('zz_auth_'.$server_ip,md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM'),3600*24);
        		}else{
        			$this->error($zz_query['msg']);
        		}
        	}else{
        	    $this->error('查询授权信息失败');
        	}
        }
	}
	
    public function index()
    {
        $key = get_setting('cron_key');
        $http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
        $url = $http_type . $_SERVER['HTTP_HOST'];//  . $_SERVER['REQUEST_URI']
        
        // 获取定时任务错误信息
        $cronErrors = cache('cron_job_errors') ?: [];
        
        return view('',['key' => $key,'url' => $url,'root' => root_path(), 'cronErrors' => $cronErrors]);
    }
    
    /**
     * 清除定时任务错误日志
     */
    public function clearErrors()
    {
        cache('cron_job_errors', null);
        return json(['code' => 1, 'msg' => '清除成功']);
    }
    
    /**
     * 检查定时任务运行状态
     */
    public function checkStatus()
    {
        // 检查定时任务状态
        $cronStatus = $this->checkProcessRunning(['php82 think cron', 'php think cron']);
        
        // 检查消息队列状态
        $queueStatus = $this->checkProcessRunning(['php82 think queue:listen', 'php think queue:listen', 'php82 think queue:work', 'php think queue:work']);
        
        // 检查队列中是否有待处理的任务
        if ($queueStatus) {
            $queueStatus = $this->checkQueueJobs();
        }
        
        return json([
            'code' => 1,
            'data' => [
                'cron' => $cronStatus,
                'queue' => $queueStatus
            ]
        ]);
    }
    
    /**
     * 检查进程是否运行
     */
    private function checkProcessRunning($processNames)
    {
        // Windows系统
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            // 检查是否有PHP进程在运行
            $command = 'tasklist /FI "IMAGENAME eq php.exe" /FO CSV';
            $output = shell_exec($command);
            if (empty($output)) {
                return false;
            }
            
            $lines = explode("\n", trim($output));
            
            // 检查每个进程名
            foreach ($processNames as $processName) {
                foreach ($lines as $line) {
                    // 跳过CSV标题行
                    if (strpos($line, '"映像名称"') !== false) {
                        continue;
                    }
                    if (strpos($line, $processName) !== false) {
                        return true;
                    }
                }
            }
            return false;
        }
        // Linux系统
        else {
            // 检查每个进程名
            foreach ($processNames as $processName) {
                $command = "ps aux | grep '{$processName}' | grep -v grep";
                $output = shell_exec($command);
                if (!empty(trim($output))) {
                    return true;
                }
            }
            return false;
        }
    }
    
    /**
     * 检查队列中是否有待处理的任务
     */
    private function checkQueueJobs()
    {
        try {
            // 检查队列任务表是否有待处理的任务
            $queueTable = config('queue.connections.database.table', 'jobs');
            $count = \think\facade\Db::table($queueTable)
                ->where('attempts', '<', 3) // 尝试次数少于3次
                ->where('reserved_at', 0) // 未被保留
                ->orWhere('reserved_at', '<', time() - 3600) // 或者保留时间超过1小时
                ->count();
            
            return $count > 0;
        } catch (\Exception $e) {
            // 如果查询失败，假设队列正在运行
            return true;
        }
    }
}