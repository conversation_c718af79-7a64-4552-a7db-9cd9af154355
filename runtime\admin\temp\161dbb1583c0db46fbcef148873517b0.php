<?php /*a:1:{s:59:"E:\phpEnv\www\pay.cn\app\admin\view\system\passage\add.html";i:1753008470;}*/ ?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>通道添加</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/admin/lib/layui-v2.11.4/css/layui.css" media="all">
    <link rel="stylesheet" href="/static/admin/css/public.css" media="all">
    <link rel="stylesheet" href="/static/admin/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <style>
        .layui-input-block tip {
            color: #a29c9c;
            font-size: 12px;
            line-height: 1.5;
            display: block;
            margin-top: 5px;
        }
        #hang_free_img,.pay-field,.layuimini-main .layui-form .layui-form-item .pay-dcep,.pay-alipay2{
            display: none;
        }
        .pay-wx-native-v3, .pay-alipay-api {
            display: none;
        }
    </style>
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main">
            <div class="layui-form layuimini-form">
                <input type="hidden" name="status" value="1">
                <input type="hidden" name="hang_free" value="0">
                <input type="hidden" name="money_change" value="0">
                <input type="hidden" name="cookie" value="">
                <div class="layui-form-item">
                    <label class="layui-form-label required">支付模式</label>
                    <div class="layui-input-block">
                        <select name="type" lay-verify="required" lay-filter="type">
                            <option value="">直接选择或搜索选择</option> 
                            <?php foreach($payType as $k => $v): ?>
                            <option value="<?php echo htmlentities((string) $k); ?>"><?php echo htmlentities((string) $v); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label required">通道模式</label>
                    <div class="layui-input-block">
                        <select name="code" lay-verify="required" lay-filter="code">
                            <option value="">直接选择或搜索选择</option>
                            <?php foreach($payCode as $k => $v): ?>
                            <option value="<?php echo htmlentities((string) $k); ?>" class="code-option code-option-<?php echo htmlentities((string) $k); ?>"><?php echo htmlentities((string) $v); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item pay-field pay-alipay pay-dcep">
                    <label class="layui-form-label" id="zf_pid_label">支付用户ID</label>
                    <div class="layui-input-block">
                        <input type="text" name="zf_pid" placeholder="请输入支付用户ID" value="" class="layui-input" id="zf_pid_input">
                        <tip id="zf_pid_tip">支付宝填写（使用免挂会自动填写）可免输入，数字人民币填写钱包编码（优先使用收款码）</tip>
                    </div>
                </div>
                <div class="ewm-fields" style="display:none;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">收款码类型</label>
                        <div class="layui-input-block">
                            <select name="ewm_type" lay-filter="ewm_type">
                                <option value="0">二维码</option>
                                <option value="1">赞赏码</option>
                            </select>
                            <tip>二维码则是解码后的二维码地址，赞赏码是直接上传的图片</tip>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">无金额收款码</label>
                        <div class="layui-input-block layuimini-upload">
                            <input name="ewm" class="layui-input layui-col-xs6" placeholder="请上传收款码" lay-verify="required" value="">
                            <div class="layuimini-upload-btn">
                                <span><a class="layui-btn layui-btn-normal" id="test8"><i class="fa fa-upload"></i> 上传二维码</a></span>
                                <span><a class="layui-btn" id="test9"><i class="fa fa-upload"></i> 上传赞赏码</a></span>
                            </div>
                            <tip class="ewm-tip-normal">这里是默认二维码，有上传有金额收款码会优先使用，如果遇到解码失败，请到<a href="https://cli.im/deqr/" target="_blank">https://cli.im/deqr/</a>，复制解码后的地址</tip>
                            <tip class="ewm-tip-dcep pay-field pay-dcep">数字人民币使用无限制时长收款码：开通网商银行钱包然后在钱包快付管理开通支付宝，在支付宝找到数字人民币可以保存收款码无限制时效</tip>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item pay-field pay-alipay" id="money_change_div">
                    <label class="layui-form-label">支付宝免递增金额</label>
                    <div class="layui-input-block">
                        <input type="checkbox" id="money_change" lay-skin="switch" lay-text="开启|禁用" lay-filter="money_change">
                        <tip style="vertical-align: middle;">开启后必须支付宝免CK选择账单模式或者监控端开启免递增金额，付款时需要备注订单ID，否则不回调</tip>
                    </div>
                </div>
                <div id="hang_free_box" class="pay-field pay-alipay pay-qq">
                    <div class="layui-form-item">
                        <label class="layui-form-label">免挂</label>
                        <div class="layui-input-block">
                            <input type="checkbox" id="hang_free" lay-skin="switch" lay-text="开启|禁用" lay-filter="hang_free">
                            <tip style="vertical-align: middle;display: inline-block;
    margin-top: 10px;
    line-height: 15px;
    color: #a29c9c;">使用支付宝免CK不需要挂监控端；使用监控端云端模式会自动更新CK，监控端不用开心跳，监控端心跳无效，需要使用监控端心跳请关闭此项</tip>
                        </div>
                    </div>
                    <div class="layui-form-item" id="hang_free_img">
                        <label class="layui-form-label">登录二维码</label>
                        <div class="layui-input-block">
                            <img src="" style="width: 200px;">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item pay-field pay-alipay">
                    <label class="layui-form-label">支付宝免CK</label>
                    <div class="layui-input-block">
                        <select name="avoid_ck" lay-filter="avoid_ck">
                            <option value="0">关闭</option>
                            <option value="1">余额模式</option>
                            <option value="2">账单模式</option>
                        </select>
                        <tip>开启免挂后再开启此项，填写支付用户ID（支付宝PID）、应用AppID、支付宝公钥、应用私钥；余额模式秒回调，账单模式10秒回调，支持免递增金额；<a href="https://www.zzwws.cn/archives/6450/" target="_blank">配置教程</a></tip>
                    </div>
                </div>
                <div class="layui-form-item pay-field pay-alipay2">
                    <label class="layui-form-label required">应用AppID</label>
                    <div class="layui-input-block">
                        <input type="text" name="app_id" placeholder="请输入应用AppID" value="" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item pay-field pay-alipay2">
                    <label class="layui-form-label required">支付宝公钥</label>
                    <div class="layui-input-block">
                        <input type="text" name="public_key" placeholder="请输入支付宝公钥" value="" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item pay-field pay-alipay2">
                    <label class="layui-form-label required">应用私钥</label>
                    <div class="layui-input-block">
                        <input type="text" name="private_key" placeholder="请输入应用私钥" value="" class="layui-input">
                    </div>
                </div>
                <div class="pay-field pay-alipay-api" style="display: none;">
                    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
                        <legend>支付宝API配置</legend>
                    </fieldset>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">AppID</label>
                        <div class="layui-input-block">
                            <input type="text" name="alipay_config[app_id]" placeholder="请输入支付宝应用APPID" value="" class="layui-input">
                            <tip>支付宝开放平台应用的AppID</tip>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">应用私钥</label>
                        <div class="layui-input-block">
                            <textarea name="alipay_config[private_key]" placeholder="请输入应用私钥" class="layui-textarea" rows="3"></textarea>
                            <tip>应用私钥，请确保格式正确</tip>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">支付宝公钥</label>
                        <div class="layui-input-block">
                            <textarea name="alipay_config[public_key]" placeholder="请输入支付宝公钥" class="layui-textarea" rows="3"></textarea>
                            <tip>支付宝公钥，从支付宝开放平台获取</tip>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">回调地址</label>
                        <div class="layui-input-block">
                            <input type="text" name="alipay_config[notify_url]" placeholder="异步通知回调地址（可选）" value="" class="layui-input">
                            <tip>支付结果异步通知地址，如果为空则使用默认地址</tip>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">返回地址</label>
                        <div class="layui-input-block">
                            <input type="text" name="alipay_config[return_url]" placeholder="同步返回地址（可选）" value="" class="layui-input">
                            <tip>支付完成后页面跳转地址，如果为空则使用默认地址</tip>
                        </div>
                    </div>
                </div>
                <div class="pay-field pay-wx-native-v3">
                    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
                        <legend>微信 NativeV3 配置</legend>
                    </fieldset>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">AppID</label>
                        <div class="layui-input-block">
                            <input type="text" name="wechat_config[appid]" placeholder="请输入微信 AppID" value="" class="layui-input">
                            <tip>微信公众号或者小程序的AppID</tip>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">商户号(MCHID)</label>
                        <div class="layui-input-block">
                            <input type="text" name="wechat_config[mchid]" placeholder="请输入微信支付商户号" value="" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">APIv3密钥</label>
                        <div class="layui-input-block">
                            <input type="text" name="wechat_config[apiv3_key]" placeholder="请输入 APIv3 密钥" value="" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">商户证书序列号</label>
                        <div class="layui-input-block">
                            <input type="text" name="wechat_config[serial_no]" placeholder="请输入商户证书序列号" value="" class="layui-input">
                            <tip>打开商户证书 `apiclient_cert.pem` 查看</tip>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">商户私钥</label>
                        <div class="layui-input-block layuimini-upload">
                            <input name="wechat_config[key_path]" class="layui-input layui-col-xs6" placeholder="请上传商户API私钥文件 apiclient_key.pem" value="">
                            <div class="layuimini-upload-btn">
                                <span><a class="layui-btn layui-btn-normal" id="upload-key-pem"><i class="fa fa-upload"></i> 上传文件</a></span>
                            </div>
                            <tip>请上传 apiclient_key.pem 文件</tip>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">平台证书</label>
                        <div class="layui-input-block layuimini-upload">
                            <input name="wechat_config[cert_path]" class="layui-input layui-col-xs6" placeholder="请上传平台证书 wechatpay_cert.pem" value="">
                            <div class="layuimini-upload-btn">
                                <span><a class="layui-btn layui-btn-normal" id="upload-cert-pem"><i class="fa fa-upload"></i> 上传文件</a></span>
                            </div>
                            <tip>请上传 wechatpay_cert.pem 文件</tip>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">区分方式</label>
                    <div class="layui-input-block">
                        <select name="pay_qf">
                            <option value="0">金额递增</option>
                            <option value="1">金额递减</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item pay-field pay-alipay">
                    <label class="layui-form-label">支付宝转账类型</label>
                    <div class="layui-input-block">
                        <select name="transfer">
                            <option value="0">关闭</option>
                            <option value="1">个人转账</option>
                            <option value="4">转账确认单</option>
                            <option value="3">转入小钱袋</option>
                            <option value="2">扫码转账</option>
                        </select>
                        <tip>个人转账和转入小钱袋不支持APP监控，关闭后就是收款码</tip>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">单笔限额</label>
                    <div class="layui-input-block">
                        <input type="number" name="single_limit" placeholder="单笔交易限额，0为不限制" value="0" step="0.01" min="0" class="layui-input">
                        <tip>单笔交易最大金额限制，超过此金额的订单将不会分配到此通道，0表示不限制</tip>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">单日限额</label>
                    <div class="layui-input-block">
                        <input type="number" name="daily_limit" placeholder="单日交易限额，0为不限制" value="0" step="0.01" min="0" class="layui-input">
                        <tip>单日累计交易金额限制，达到此金额后通道将停止接收新订单，0表示不限制</tip>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <input type="text" name="remarks" placeholder="请输入备注" value="" class="layui-input">
                    </div>
                </div>
                <!-- <div class="layui-form-item">
                    <label class="layui-form-label">解码接口测试</label>
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn layui-btn-normal" id="testDecodeBtn">
                            <i class="fa fa-test"></i> 测试解码接口
                        </button>
                        <tip>点击测试当前配置的二维码解码接口是否正常工作</tip>
                    </div>
                </div> -->
                <div class="layui-form-item">
                    <label class="layui-form-label required">状态</label>
                    <div class="layui-input-block">
                        <input type="checkbox" lay-skin="switch" lay-text="开启|禁用" lay-filter="status" checked>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-normal" lay-submit lay-filter="saveBtn">确认保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/static/admin/lib/layui-v2.11.4/layui.js" charset="utf-8"></script>
    <script>
        layui.use(['form','upload'], function () {
            var form = layui.form,
                layer = layui.layer,
                $ = layui.$,
                upload = layui.upload;

            var allPayFields = $('.pay-field');
            var ewmFields = $('.ewm-fields');
            var payAlipayLegacy = $('.pay-alipay');
            var payAlipayApi = $('.pay-alipay-api');
            var payWxApi = $('.pay-wx-native-v3');
            var payAlipay2 = $('.pay-alipay2');
            var hangFreeBox = $('#hang_free_box');
            var moneyChangeDiv = $('#money_change_div');

            function toggleCodeFields(payType, code) {
                payType = parseInt(payType);
                code = parseInt(code);

                allPayFields.hide();
                ewmFields.hide();
                payAlipayLegacy.hide();
                payAlipayApi.hide();
                payWxApi.hide();
                payAlipay2.hide();
                hangFreeBox.hide();
                moneyChangeDiv.hide();

                var qrMonitoringCodes = [1, 2, 3, 6, 7, 8, 13, 14, 15, 16, 17];
                var alipayApiCodes = [10, 11, 12];
                var wechatApiCodes = [4, 5];

                if (qrMonitoringCodes.includes(code)) {
                    ewmFields.show();
                }

                if (alipayApiCodes.includes(code)) {
                    payAlipayApi.show();
                }

                if (wechatApiCodes.includes(code)) {
                    payWxApi.show();
                    if (code === 5) { // JSAPI
                        payWxApi.find('legend').text('微信 JSAPI 配置');
                    } else {
                        payWxApi.find('legend').text('微信 NativeV3 配置');
                    }
                }

                if (code === 9) { // 支付宝商家账单
                    ewmFields.show();
                    payAlipayLegacy.show();
                    hangFreeBox.show();
                    moneyChangeDiv.show();
                    if ($('select[name="avoid_ck"]').val() != 0) {
                        payAlipay2.show();
                    }
                }
                
                if (code === 15) {
                    hangFreeBox.show();
                }
            }

            form.on('select(type)', function(data){
                var payType = data.value;
                var codeSelect = $('select[name="code"]');
                codeSelect.empty();
                codeSelect.append('<option value="">请选择通道模式</option>');

                switch(payType) {
                    case '1': // 微信
                        codeSelect.append('<option value="1">微信个人码-监控端</option>');
                        codeSelect.append('<option value="2">微信经营码-监控端</option>');
                        codeSelect.append('<option value="3">微信收款单-监控端</option>');
                        codeSelect.append('<option value="4">微信-NativeV3</option>');
                        codeSelect.append('<option value="5">微信-JSAPI</option>');
                        codeSelect.append('<option value="6">微信拉卡拉-监控端</option>');
                        codeSelect.append('<option value="7">微信收钱吧-监控端</option>');
                        break;
                    case '2': // 支付宝
                        codeSelect.append('<option value="8">支付宝个人码-监控端</option>');
                        codeSelect.append('<option value="9">支付宝商家账单</option>');
                        codeSelect.append('<option value="10">支付宝当面付</option>');
                        codeSelect.append('<option value="11">支付宝手机网站支付</option>');
                        codeSelect.append('<option value="12">支付宝电脑网站支付</option>');
                        codeSelect.append('<option value="13">支付宝拉卡拉-监控端</option>');
                        codeSelect.append('<option value="14">支付宝收钱吧-监控端</option>');
                        break;
                    case '3': // QQ
                        codeSelect.append('<option value="15">QQ个人码-监控端</option>');
                        break;
                    case '4': // 数字人民币
                        codeSelect.append('<option value="16">数字人民币钱包码-监控端</option>');
                        break;
                }
                form.render('select');
                toggleCodeFields(payType, null);
            });

            form.on('select(code)', function(data){
                var payType = $('select[name="type"]').val();
                toggleCodeFields(payType, data.value);

                // 根据通道模式更新支付用户ID的标签和提示
                updatePaymentUserIdField(parseInt(payType), parseInt(data.value));
            });

            form.on('select(avoid_ck)', function(data){
                if($('select[name="code"]').val() == 9 && data.value != 0){
                    $('.pay-alipay2').show();
                }else{
                    $('.pay-alipay2').hide();
                }
            });

            // 更新支付用户ID字段的函数
            function updatePaymentUserIdField(payType, code) {
                var label = $('#zf_pid_label');
                var input = $('#zf_pid_input');
                var tip = $('#zf_pid_tip');

                if (payType === 4 && code === 16) { // 数字人民币钱包码-监控端
                    label.text('钱包编码');
                    input.attr('placeholder', '请输入钱包编码');
                    tip.text('数字人民币填写钱包编码（优先使用收款码）');
                    $('.pay-dcep').show();
                } else if (payType === 2) { // 支付宝相关
                    label.text('支付用户ID');
                    input.attr('placeholder', '请输入支付用户ID');
                    tip.text('支付宝填写（使用免挂会自动填写）可免输入');
                } else {
                    label.text('支付用户ID');
                    input.attr('placeholder', '请输入支付用户ID');
                    tip.text('请根据对应支付方式填写相应的用户标识');
                }
            }

            var initialPayType = $('select[name="type"]').val();
            var initialCode = $('select[name="code"]').val();
            toggleCodeFields(initialPayType, initialCode);
            if (initialPayType && initialCode) {
                updatePaymentUserIdField(parseInt(initialPayType), parseInt(initialCode));
            }

            // 测试解码接口
            $('#testDecodeBtn').on('click', function() {
                layer.confirm('确定要测试解码接口吗？', {icon: 3, title:'提示'}, function(index){
                    layer.close(index);
                    layer.load(2, {content: '正在测试解码接口...'});
                    
                    $.post('<?php echo url("system.passage/testDecodeApi"); ?>', {}, function(res){
                        layer.closeAll('loading');
                        if(res.code == 1){
                            layer.alert('测试成功！<br>' + 
                                      '接口响应：' + res.data.message + '<br>' +
                                      '解码结果：' + (res.data.decoded_content || '无'), 
                                      {icon: 1, title: '测试结果'});
                        } else {
                            layer.alert('测试失败：' + res.msg, {icon: 2, title: '测试结果'});
                        }
                    }, 'json').fail(function(){
                        layer.closeAll('loading');
                        layer.alert('测试请求失败，请检查网络连接', {icon: 2, title: '测试结果'});
                    });
                });
            });

            //监听提交
            form.on('submit(saveBtn)', function (data) {
                $.post("<?php echo url('system.passage/add'); ?>", data.field, function (res) {
                    icon = res.code == 1 ? 1 : 2;
                    layer.msg(res.msg, { time: 2000, icon: icon }, function () {
                        if (res.code == 1) {
                            window.parent.location.reload();
                            index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        }
                    })
                })

                return false;
            });

            // 监听状态事件
            form.on('switch(status)', function (obj) {
                var status = obj.elem.checked ? 1 : 0;
                $('[name="status"]').val(status);
            });
            form.on('switch(money_change)', function (obj) {
                var status = obj.elem.checked ? 1 : 0;
                $('[name="money_change"]').val(status);
            });
            form.on('switch(hang_free)', function (obj) {
                var status = obj.elem.checked ? 1 : 0;
                if(status == 1){
                    $("#hang_free_img").show();
                    $.post('<?php echo url("system.passage/hangFree"); ?>',{act: 'login',type: $('[name="type"]').val()},function(res){
                        if(res.code == 1){
                            $('[name="hang_free"]').val(status);
                            if($('[name="type"]').val() == 3){
                                $("#hang_free_img img").attr('src','data:image/jpg/png/gif;base64,'+res.data.qrcodeurl);
                            }else{
                                $("#hang_free_img img").attr('src','/enQrcode?url='+res.data.qrcodeurl);
                            }
                            var t2 = window.setInterval(function() {
                                $.ajaxSettings.async = false;
                                $.post('<?php echo url("system.passage/hangFree"); ?>',{act: 'status',type: $('[name="type"]').val(),loginid: res.data.loginid},function(res2){
                                    if(res2.code == 1){
                                        $('[name="cookie"]').val(res2.data.cookie);
                                        if(typeof res2.data.ali_uid !== "undefined"){
                                            $('[name="zf_pid"]').val(res2.data.ali_uid);
                                        }
                                        layer.msg('更新成功',{time: 1500,icon: 1},function(){
                                            window.clearInterval(t2);
                                        });
                                    }
                                },'json')
                                $.ajaxSettings.async = true;
                            },1500)
                        }else{
                            layer.msg(res.msg,{time: 1500,icon: 2});
                            $('[name="hang_free"]').val(0);
                            $('#hang_free').attr('checked',false);
                            form.render();
                            $("#hang_free_img").hide();
                        }
                    })
                }else{
                    $('[name="hang_free"]').val(status);
                    $("#hang_free_img").hide();
                }
            });

            upload.render({
                elem: '#test8',
                url: '<?php echo url('index/upload'); ?>',
                auto: true,
                done: function (res) {
                    if(res.code == 1){
                        layer.load(3);
                        $.post('<?php echo url('system.passage/decodeQrcode'); ?>',{'url': res.data.url},function(res2){
                            layer.closeAll('loading');
                            if(res2.code == 1){
                                if($('[name="ewm_type"]').val() != 0){
                                    layer.msg('当前收款码类型不是二维码',{time: 1500,icon: 2});
                                }else{
                                    layer.msg('解码成功',{time: 1500,icon: 1});
                                    $('[name="ewm"]').val(res2.data.url);
                                }
                            }else{
                                layer.msg(res2.msg,{time: 1500,icon: 2});
                            }
                        },'json')
                    }else{
                        layer.msg(res.msg,{time: 1500,icon: 2});
                    }
                }
            });

            upload.render({
                elem: '#test9',
                url: '<?php echo url('index/upload'); ?>',
                auto: true,
                done: function (res) {
                    var icon = res.code == 1 ? 1 : 2;
                    if (res.code == 1) {
                        if($('[name="ewm_type"]').val() != 1){
                            layer.msg('当前收款码类型不是赞赏码',{time: 1500,icon: 2});
                        }else{
                            layer.msg('上传成功',{time: 1500,icon: 1});
                            $('[name="ewm"]').val(res.data.url);
                        }
                    }else{
                        layer.msg(res.msg,{time: 1500,icon: 2});
                    }
                }
            });
            
            upload.render({
                elem: '#upload-key-pem',
                url: '<?php echo url('index/upload'); ?>',
                accept: 'file',
                done: function(res){
                    if(res.code == 1){
                        layer.msg('上传成功', {time: 1000, icon: 1});
                        $('input[name="wechat_config[key_path]"]').val(res.data.url);
                    }else{
                        layer.msg(res.msg, {time: 1000, icon: 2});
                    }
                }
            });

            upload.render({
                elem: '#upload-cert-pem',
                url: '<?php echo url('index/upload'); ?>',
                accept: 'file',
                done: function(res){
                    if(res.code == 1){
                        layer.msg('上传成功', {time: 1000, icon: 1});
                        $('input[name="wechat_config[cert_path]"]').val(res.data.url);
                    }else{
                        layer.msg(res.msg, {time: 1000, icon: 2});
                    }
                }
            });
        });
    </script>
</body>

</html>