<?php
namespace app\admin\controller\system;

use app\common\controller\CommonController;
use app\admin\model\SystemConfig;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;

/**
 * @ControllerAnnotation(title="配置管理")
 */
class Config extends CommonController
{
	public function __construct()
	{
	    $authcode = get_setting('authcode');
        if(!$authcode){
            $this->error('请填写授权码');
        }
        $zz_id = '2';
        $zz_url = 'auth.zzwws.cn';
        $root = $_SERVER['DOCUMENT_ROOT'];
        $host = $_SERVER['HTTP_HOST'];
        $server_ip = !empty($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : $_SERVER['APP_POOL_ID'];
        $date = date("Y-m-d");
        $zz_auth = cache('zz_auth_'.$server_ip);
        if(md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM') !== $zz_auth){
        $zz_query=get_curl('http://'.$zz_url.'/ZR/api/zr_check.php?url='.$host.'&authcode='.$authcode.'&version=&ZRid='.$zz_id);
            if ($zz_query = json_decode($zz_query, true)) {
        		if ($zz_query["code"] == 0) {
        			cache('zz_auth_'.$server_ip,md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM'),3600*24);
        		}else{
        			$this->error($zz_query['msg']);
        		}
        	}else{
        	    $this->error('查询授权信息失败');
        	}
        }
        
		$this->model = new SystemConfig();
	}

	/**
	 * @NodeAnotation(title="编辑")
	 */
	public function edit()
	{
		if(request()->isAjax()){
			return $this->model->edit();
		}

		$config = $this->model->select()->toArray();
		$data = [];
		foreach($config as $v){
			$data[$v['k']] = $v['v'];
		}
		return view('',$data);
	}
}