<?php
/**
 * 企业微信消息解密快速测试脚本
 * 用于快速测试和调试企业微信消息解密问题
 */

// 引入企业微信加密库
require_once __DIR__ . '/extend/callback/ErrorCode.php';
require_once __DIR__ . '/extend/callback/SHA1.php';
require_once __DIR__ . '/extend/callback/XMLParse.php';
require_once __DIR__ . '/extend/callback/PKCS7Encoder.php';
require_once __DIR__ . '/extend/callback/Prpcrypt.php';
require_once __DIR__ . '/extend/callback/WXBizMsgCrypt.php';

use callback\WXBizMsgCrypt;

echo "=== 企业微信消息解密快速测试 ===\n\n";

// 配置信息（请根据实际情况修改）
$token = 'your_token_here';  // 请替换为实际的Token
$encodingAESKey = 'your_encoding_aes_key_here';  // 请替换为实际的EncodingAESKey（43位）
$corpId = 'your_corp_id_here';  // 请替换为实际的CorpId（以ww开头）

// 从日志中获取的实际测试数据（请根据实际日志修改）
$testCases = [
    [
        'name' => '测试案例1 - 来自日志',
        'msgSignature' => '8db60461e03ef192d27e1d1fd0f7e82f26859290',
        'timestamp' => '1755757132',
        'nonce' => '1755712868',
        'encryptMsg' => '<xml><ToUserName><![CDATA[ww666a49fdd2b8622e]]></ToUserName><Encrypt><![CDATA[6asDaxIMnwc2KtxWIs3DDUymVkoNzPQk4q0TPVNgkm0hI/uZ7y9EpZz4SP6iJFfIcvabJ5R+OORok3s+bgd4eJ8+kJ2ikm1ZxX0uBjGGelHEvwRA2BFdhMvoFuNEV7u+BGy+kYcxU+dscqNff0ShK6MeoXPiMiP5mDUbGQs2rndnBIhGz818fU9fr9JNovs2pPG8xQ9GjMFVclpxQvti3jAByyjCUGVYgwEoyMa2uoBdAKrl4WrPvSAAAme8bk/jrxOfHz8u7Gq+/ivuIBma/5hRsmyrmMMRBcWUakB0HsmkyVXPEAPXiBFm6HMf5Y7LBhHA8OfuksDJRhLDoViZWa94QO35T+Hc4XVMyQW5YiIJAwdeaNoMaDppc2fX7Uf/HFWEvuM72bNXOb15JQkhxbBM1aoMT43TKPvxbmbq7NFisc9GBvWIEDvOb1+1AWfphrkkyBYxrLb/oq8/YEE6MTyX31No9EzJ+kaPhHkOd7mjkle7+3G0NHoKyodWQe+I]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>'
    ],
    [
        'name' => '测试案例2 - 来自日志',
        'msgSignature' => '6fabc2144d924a8532cdc4796bf6b5484f0129e7',
        'timestamp' => '1755757135',
        'nonce' => '1756217447',
        'encryptMsg' => '<xml><ToUserName><![CDATA[ww666a49fdd2b8622e]]></ToUserName><Encrypt><![CDATA[Lr1xUdnIsjd/PoLeGF8PLIpxZV9lfeviiJvt2ssRzlwCnfB+HFdbeK3exVuIm0soUi9gPCDxpSyhGPOBpmRd4ArgkbZQe/cQUIZIOT9ZirzjMF4g9TQ+yOzHogMYqswaK336FkgwtmUEn5bg0ZC/C/YbsrM5ee9O4WuGGc/bElJw06ennTS7mK4Of41cqP1AjHW5SgnKGpScqI44UAdCn2CFjomofKrKJxPkTMVcrh0bxw29IEUmRZdwGF7aLM4iAhVyITVhaYPPPtQ4DFxFwCyb9UAIp2GuXK80leV215AExkUKYFbtgtAkqri13TCKxICnHtkhpP3swWZUsa7vL294h6kYPcRqDpQWzdEnW1DWUPlA+lJKrExuxPyHeVWuUdO59yof5jYefhuSwe7nUiGpauWkTgqDzLksiHhmsHFy5Bob5gqluMNt+PTHf/thJM2NRpIEMp/M7gk9QG6CSSEwWnDyOUExq1GaP924IBVYRonQXDmV9P4cwfhttGX/]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>'
    ]
];

/**
 * 检查配置
 */
function checkConfig($token, $encodingAESKey, $corpId)
{
    echo "检查配置...\n";
    
    $errors = [];
    
    if (empty($token) || $token === 'your_token_here') {
        $errors[] = 'Token未设置或使用默认值';
    } else {
        echo "✅ Token已设置，长度: " . strlen($token) . "\n";
    }
    
    if (empty($encodingAESKey) || $encodingAESKey === 'your_encoding_aes_key_here') {
        $errors[] = 'EncodingAESKey未设置或使用默认值';
    } elseif (strlen($encodingAESKey) !== 43) {
        $errors[] = 'EncodingAESKey长度错误，应为43位，当前为' . strlen($encodingAESKey) . '位';
    } else {
        echo "✅ EncodingAESKey已设置，长度正确\n";
    }
    
    if (empty($corpId) || $corpId === 'your_corp_id_here') {
        $errors[] = 'CorpId未设置或使用默认值';
    } elseif (!preg_match('/^ww[a-zA-Z0-9]{14,16}$/', $corpId)) {
        $errors[] = 'CorpId格式错误，应以ww开头，当前为: ' . $corpId;
    } else {
        echo "✅ CorpId已设置，格式正确\n";
    }
    
    if (!empty($errors)) {
        echo "\n❌ 配置错误:\n";
        foreach ($errors as $error) {
            echo "   - $error\n";
        }
        echo "\n请修改脚本顶部的配置信息后重新运行\n";
        return false;
    }
    
    echo "✅ 配置检查通过\n\n";
    return true;
}

/**
 * 测试解密
 */
function testDecrypt($token, $encodingAESKey, $corpId, $testCase)
{
    echo "=== " . $testCase['name'] . " ===\n";
    echo "msgSignature: " . $testCase['msgSignature'] . "\n";
    echo "timestamp: " . $testCase['timestamp'] . "\n";
    echo "nonce: " . $testCase['nonce'] . "\n";
    echo "encryptMsg长度: " . strlen($testCase['encryptMsg']) . "\n\n";
    
    try {
        // 创建解密实例
        $wxCrypt = new WXBizMsgCrypt($token, $encodingAESKey, $corpId);
        
        // 解密消息
        $decryptMsg = '';
        $errCode = $wxCrypt->DecryptMsg(
            $testCase['msgSignature'],
            $testCase['timestamp'],
            $testCase['nonce'],
            $testCase['encryptMsg'],
            $decryptMsg
        );
        
        if ($errCode === 0) {
            echo "✅ 解密成功!\n";
            echo "解密后的消息:\n";
            echo $decryptMsg . "\n\n";
            
            // 解析XML消息
            $xml = simplexml_load_string($decryptMsg, 'SimpleXMLElement', LIBXML_NOCDATA);
            if ($xml) {
                echo "📋 消息详情:\n";
                echo "   ToUserName: " . (string)$xml->ToUserName . "\n";
                echo "   FromUserName: " . (string)$xml->FromUserName . "\n";
                echo "   CreateTime: " . (string)$xml->CreateTime . " (" . date('Y-m-d H:i:s', (int)$xml->CreateTime) . ")\n";
                echo "   MsgType: " . (string)$xml->MsgType . "\n";
                if (isset($xml->Content)) {
                    echo "   Content: " . (string)$xml->Content . "\n";
                }
                if (isset($xml->MsgId)) {
                    echo "   MsgId: " . (string)$xml->MsgId . "\n";
                }
                if (isset($xml->AgentID)) {
                    echo "   AgentID: " . (string)$xml->AgentID . "\n";
                }
            }
        } else {
            echo "❌ 解密失败，错误码: $errCode\n";
            echo "错误说明: " . getErrorCodeName($errCode) . "\n";
            
            if ($errCode == -40001) {
                analyzeSignatureError($token, $testCase['msgSignature'], $testCase['timestamp'], $testCase['nonce'], $testCase['encryptMsg']);
            }
        }
        
    } catch (Exception $e) {
        echo "❌ 解密过程中发生异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
}

/**
 * 获取错误码名称
 */
function getErrorCodeName($code)
{
    $errorNames = [
        0 => '成功',
        -40001 => '签名验证错误',
        -40002 => 'XML解析失败',
        -40003 => 'SHA加密失败',
        -40004 => 'EncodingAESKey非法',
        -40005 => 'CorpID校验失败',
        -40006 => 'AES加密失败',
        -40007 => 'AES解密失败',
        -40008 => '解密后buffer非法',
        -40009 => 'Base64加密失败',
        -40010 => 'Base64解密失败',
        -40011 => '生成XML失败'
    ];
    
    return $errorNames[$code] ?? '未知错误';
}

/**
 * 分析签名验证错误
 */
function analyzeSignatureError($token, $msgSignature, $timestamp, $nonce, $encryptMsg)
{
    echo "\n🔍 分析签名验证错误:\n";
    
    // 解析XML获取Encrypt内容
    $encrypt = '';
    if (strpos($encryptMsg, '<xml>') !== false) {
        $xml = simplexml_load_string($encryptMsg);
        $encrypt = (string)$xml->Encrypt;
    } else {
        $encrypt = $encryptMsg;
    }
    
    echo "Encrypt内容前50字符: " . substr($encrypt, 0, 50) . "...\n";
    echo "Encrypt内容长度: " . strlen($encrypt) . "\n\n";
    
    // 尝试不同的签名组合
    echo "尝试不同的签名组合:\n";
    
    $combinations = [
        '标准组合 (token, timestamp, nonce, encrypt)' => [$token, $timestamp, $nonce, $encrypt],
        '不含encrypt (token, timestamp, nonce)' => [$token, $timestamp, $nonce],
        '顺序1 (timestamp, nonce, encrypt, token)' => [$timestamp, $nonce, $encrypt, $token],
        '顺序2 (timestamp, nonce, token)' => [$timestamp, $nonce, $token],
        '顺序3 (encrypt, token, timestamp, nonce)' => [$encrypt, $token, $timestamp, $nonce]
    ];
    
    foreach ($combinations as $name => $params) {
        sort($params, SORT_STRING);
        $sortedStr = implode($params);
        $signature = sha1($sortedStr);
        
        echo "   $name: $signature";
        if ($signature === $msgSignature) {
            echo " ✅ 匹配!";
        }
        echo "\n";
    }
    
    echo "\n实际签名: $msgSignature\n";
    echo "Token: $token\n";
    echo "Timestamp: $timestamp\n";
    echo "Nonce: $nonce\n";
}

// 主程序
if (!checkConfig($token, $encodingAESKey, $corpId)) {
    exit(1);
}

// 运行测试案例
foreach ($testCases as $testCase) {
    testDecrypt($token, $encodingAESKey, $corpId, $testCase);
}

echo "=== 测试完成 ===\n";
echo "\n💡 如果所有测试都失败，请检查:\n";
echo "1. Token、EncodingAESKey、CorpId是否正确\n";
echo "2. 企业微信管理后台的配置是否与代码一致\n";
echo "3. 是否使用了正确的应用配置\n";
echo "4. 配置中是否包含多余的空格或特殊字符\n";
