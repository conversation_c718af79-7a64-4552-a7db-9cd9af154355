<?php
// 引入必要的文件
require_once 'extend/callback/ErrorCode.php';
require_once 'extend/callback/SHA1.php';
require_once 'extend/callback/XMLParse.php';
require_once 'extend/callback/PKCS7Encoder.php';
require_once 'extend/callback/Prpcrypt.php';
require_once 'extend/callback/WXBizMsgCrypt.php';

use callback\WXBizMsgCrypt;

// 企业微信配置参数
$token = 'XjQLm4iGs8YtmGQ3oE9Q0Op';
$encodingAESKey = 'X198BDkRlpg375z4hwakFKwp8MgyLa7GsRleDi8WgVw';
$corpId = 'ww666a49fdd2b8622e';

// 从日志中获取的实际消息数据（使用最新的消息）
$encryptMsg = '<xml><ToUserName><![CDATA[ww666a49fdd2b8622e]]></ToUserName><Encrypt><![CDATA[+fNiya<PERSON>uybGMUMMCFJ26WAGofsyQnFyXV6O/VxecwabfbV9x2LQspENJjga2kXC/sHEUqE9UbIqnaADtx3Ux73QwHh6dySCsDj/U1IycP8ZeoeNouIlxTgXJDVam0Jd7l6BuJSe6+lvrKcqKz8FYxoSsIZrFUJ30yR1AmIYEIykoLOYIATxX/OnMqwh6c2YCVzXfWkcsftURUcoyaRM0/sCd3pdF1BenuJvvd6F7oSysQjQiYbTa1ZDZe5cJgTYylmh87adDfXl090+/xYMNN0au6VCVWLak2xTanjoSce9Eg+ESaJXwPU4h01RXwZbk7Mu6B/rya4yoB5+xrVsYhwys+1UYckDTjQ888nOfxMA+x4yvZ+/rcsdVLfmLAzrNJmaVTMp7QL+W1shUNCIBqKinCnQZ61rwpjJ8MFR7BbqJ0fKECe7owa5W+d2burLtqMOps1EAVtRWPp+9T9DBO6t8eH/KSWTugKv982XWj8vsia+Jn5d54ODiugZYNZm/]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>';

// 从日志中获取的实际参数
$timestamp = '1755747512'; // 2025-08-21 11:38:33 的时间戳
$nonce = '1755854832'; // 从日志中获取的nonce
$msgSignature = '5b5507deb7fe828505c3513f14b80dc22d612911'; // 从日志中获取的msgSignature

// 使用实际参数进行测试
echo "=== 企业微信消息解密测试 ===\n";
echo "Token: $token\n";
echo "EncodingAESKey: $encodingAESKey\n";
echo "CorpId: $corpId\n";
echo "Timestamp: $timestamp\n";

// 创建 WXBizMsgCrypt 实例
$wxCrypt = new WXBizMsgCrypt($token, $encodingAESKey, $corpId);

// 从加密消息中提取Encrypt内容
$xmlParser = new \callback\XMLParse();
$extractResult = $xmlParser->extract($encryptMsg);

echo "\n=== 提取的加密内容 ===\n";
if (is_array($extractResult) && count($extractResult) == 2) {
    $errorCode = $extractResult[0];
    $encryptContent = $extractResult[1];
    
    if ($errorCode == 0) {
        echo "Encrypt: $encryptContent\n";
    } else {
        echo "XML解析失败，错误码: $errorCode\n";
        exit;
    }
} else {
    echo "XML解析返回格式错误\n";
    exit;
}

// 使用实际参数进行测试
echo "\n=== 使用实际参数测试 ===\n";
echo "实际Nonce: $nonce\n";
echo "实际Timestamp: $timestamp\n";
echo "实际MsgSignature: $msgSignature\n";

// 验证签名是否正确
$sha1 = new \callback\SHA1();
$signatureResult = $sha1->getSHA1($token, $timestamp, $nonce, $encryptContent);

if (is_array($signatureResult) && count($signatureResult) == 2) {
    $errorCode = $signatureResult[0];
    $calculatedSignature = $signatureResult[1];
    
    if ($errorCode == 0) {
        echo "计算得到的签名: $calculatedSignature\n";
        echo "实际接收到的签名: $msgSignature\n";
        
        if ($calculatedSignature === $msgSignature) {
            echo "签名验证成功!\n";
        } else {
            echo "签名验证失败!\n";
        }
        
        // 添加详细的签名计算调试信息
        echo "\n=== 签名计算详细信息 ===\n";
        echo "参数数组: [" . $token . ", " . $timestamp . ", " . $nonce . ", " . $encryptContent . "]\n";
        $params = array($token, $timestamp, $nonce, $encryptContent);
        sort($params, SORT_STRING);
        $sortedStr = implode($params);
        echo "排序后的字符串: $sortedStr\n";
        echo "SHA1哈希: " . sha1($sortedStr) . "\n";
        
        // 检查参数排序是否正确
        echo "\n=== 参数排序检查 ===\n";
        echo "原始参数顺序:\n";
        echo "1. Token: $token\n";
        echo "2. Timestamp: $timestamp\n";
        echo "3. Nonce: $nonce\n";
        echo "4. Encrypt: $encryptContent\n";
        
        echo "\n排序后参数顺序:\n";
        for ($i = 0; $i < count($params); $i++) {
            echo ($i + 1) . ". " . $params[$i] . "\n";
        }
        
        // 尝试不同的参数组合，看看是否能匹配接收到的签名
        echo "\n=== 尝试不同的参数组合 ===\n";
        
        // 尝试1: 交换timestamp和nonce的顺序
        $params1 = array($token, $nonce, $timestamp, $encryptContent);
        sort($params1, SORT_STRING);
        $sortedStr1 = implode($params1);
        $signature1 = sha1($sortedStr1);
        echo "组合1 (交换timestamp和nonce): $signature1\n";
        echo "是否匹配: " . ($signature1 === $msgSignature ? "是" : "否") . "\n";
        
        // 尝试2: 使用原始XML而不是提取的加密内容
        $params2 = array($token, $timestamp, $nonce, $encryptMsg);
        sort($params2, SORT_STRING);
        $sortedStr2 = implode($params2);
        $signature2 = sha1($sortedStr2);
        echo "组合2 (使用原始XML): $signature2\n";
        echo "是否匹配: " . ($signature2 === $msgSignature ? "是" : "否") . "\n";
        
        // 尝试3: 使用不同的排序方式
        $params3 = array($encryptContent, $token, $timestamp, $nonce);
        sort($params3, SORT_STRING);
        $sortedStr3 = implode($params3);
        $signature3 = sha1($sortedStr3);
        echo "组合3 (不同的参数顺序): $signature3\n";
        echo "是否匹配: " . ($signature3 === $msgSignature ? "是" : "否") . "\n";
        
        // 尝试4: 使用自然排序而不是字符串排序
        $params4 = array($token, $timestamp, $nonce, $encryptContent);
        sort($params4, SORT_NATURAL);
        $sortedStr4 = implode($params4);
        $signature4 = sha1($sortedStr4);
        echo "组合4 (使用自然排序): $signature4\n";
        echo "是否匹配: " . ($signature4 === $msgSignature ? "是" : "否") . "\n";
        
        // 尝试5: 检查是否需要URL解码
        $urlDecodedEncrypt = urldecode($encryptContent);
        $params5 = array($token, $timestamp, $nonce, $urlDecodedEncrypt);
        sort($params5, SORT_STRING);
        $sortedStr5 = implode($params5);
        $signature5 = sha1($sortedStr5);
        echo "组合5 (URL解码加密内容): $signature5\n";
        echo "是否匹配: " . ($signature5 === $msgSignature ? "是" : "否") . "\n";
        
        // 尝试6: 检查是否需要base64解码
        $base64DecodedEncrypt = base64_decode($encryptContent);
        if ($base64DecodedEncrypt !== false) {
            $params6 = array($token, $timestamp, $nonce, $base64DecodedEncrypt);
            sort($params6, SORT_STRING);
            $sortedStr6 = implode($params6);
            $signature6 = sha1($sortedStr6);
            echo "组合6 (base64解码加密内容): $signature6\n";
            echo "是否匹配: " . ($signature6 === $msgSignature ? "是" : "否") . "\n";
        } else {
            echo "组合6 (base64解码加密内容): 解码失败\n";
        }
        
        // 尝试解密消息
        $decryptMsg = '';
        $errCode = $wxCrypt->DecryptMsg($msgSignature, $timestamp, $nonce, $encryptMsg, $decryptMsg);
        
        echo "错误码: $errCode\n";
        
        if ($errCode == 0) {
            echo "解密成功!\n";
            echo "解密后的消息: $decryptMsg\n";
        } else {
            echo "解密失败!\n";
            
            // 根据错误码显示错误信息
            switch ($errCode) {
                case -40001:
                    echo "错误信息: 签名验证错误\n";
                    break;
                case -40002:
                    echo "错误信息: XML解析失败\n";
                    break;
                case -40003:
                    echo "错误信息: SHA加密失败\n";
                    break;
                case -40004:
                    echo "错误信息: AES解密失败\n";
                    break;
                case -40005:
                    echo "错误信息: 企业微信CorpId不匹配\n";
                    break;
                case -40006:
                    echo "错误信息: AES加密失败\n";
                    break;
                case -40007:
                    echo "错误信息: AES解密失败\n";
                    // 添加更详细的调试信息
                    echo "可能的原因:\n";
                    echo "1. EncodingAESKey不正确\n";
                    echo "2. CorpId不正确\n";
                    echo "3. 加密数据格式错误\n";
                    echo "4. 签名验证失败\n";
                    
                    // 检查EncodingAESKey长度
                    if (strlen($encodingAESKey) != 43) {
                        echo "EncodingAESKey长度不正确，应为43个字符，当前为" . strlen($encodingAESKey) . "个字符\n";
                    }
                    
                    // 检查CorpId
                    if (empty($corpId)) {
                        echo "CorpId为空\n";
                    } else if (!preg_match('/^ww[a-zA-Z0-9]{14,16}$/', $corpId)) {
                        echo "CorpId格式不正确，应以ww开头\n";
                    }
                    
                    // 检查timestamp格式
                    if (!is_numeric($timestamp)) {
                        echo "Timestamp不是数字格式\n";
                    }
                    
                    // 检查加密数据
                    echo "加密数据长度: " . strlen($encryptContent) . "\n";
                    echo "加密数据前50字符: " . substr($encryptContent, 0, 50) . "...\n";
                    
                    // 检查签名
                    echo "签名: $msgSignature\n";
                    echo "签名长度: " . strlen($msgSignature) . "\n";
                    
                    break;
                case -40008:
                    echo "错误信息: 时间戳参数非法\n";
                    break;
                default:
                    echo "错误信息: 未知错误\n";
                    break;
            }
        }
    } else {
        echo "签名生成失败，错误码: $errorCode\n";
    }
} else {
    echo "签名生成返回格式错误\n";
}