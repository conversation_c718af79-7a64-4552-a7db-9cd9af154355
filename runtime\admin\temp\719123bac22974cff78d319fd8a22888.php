<?php /*a:1:{s:54:"E:\phpEnv\www\pay.cn\app\admin\view\index\welcome.html";i:1755583234;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo get_setting('title'); ?></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/static/admin/lib/layui-v2.11.4/css/layui.css" media="all">
    <link rel="stylesheet" href="/static/admin/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <link rel="stylesheet" href="/static/admin/css/public.css" media="all">
    <style>
        .layui-card {border:1px solid #f2f2f2;border-radius:5px;}
        .icon {margin-right:10px;color:#1aa094;}
        .icon-cray {color:#ffb800!important;}
        .icon-blue {color:#1e9fff!important;}
        .icon-tip {color:#ff5722!important;}
        .layuimini-qiuck-module {text-align:center;margin-top: 10px}
        .layuimini-qiuck-module a i {display:inline-block;width:100%;height:60px;line-height:60px;text-align:center;border-radius:2px;font-size:30px;background-color:#F8F8F8;color:#333;transition:all .3s;-webkit-transition:all .3s;}
        .layuimini-qiuck-module a cite {position:relative;top:2px;display:block;color:#666;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;font-size:14px;}
        .welcome-module {width:100%;/*height:210px;*/}
        .panel {background-color:#fff;border:1px solid transparent;border-radius:3px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,.05);box-shadow:0 1px 1px rgba(0,0,0,.05)}
        .panel-body {padding:10px}
        .panel-title {margin-top:0;margin-bottom:0;font-size:12px;color:inherit}
        .label {display:inline;padding:.2em .6em .3em;font-size:75%;font-weight:700;line-height:1;color:#fff;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25em;margin-top: .3em;}
        .layui-red {color:red}
        .main_btn > p {height:40px;}
        .layui-bg-number {background-color:#F8F8F8;}
        .layuimini-notice:hover {background:#f6f6f6;}
        .layuimini-notice {padding:7px 16px;clear:both;font-size:12px !important;cursor:pointer;position:relative;transition:background 0.2s ease-in-out;}
        .layuimini-notice-title,.layuimini-notice-label {
            padding-right: 70px !important;text-overflow:ellipsis!important;overflow:hidden!important;white-space:nowrap!important;}
        .layuimini-notice-title {line-height:28px;font-size:14px;}
        .layuimini-notice-extra {position:absolute;top:50%;margin-top:-8px;right:16px;display:inline-block;height:16px;color:#999;}
        
        /* 新增的财务报表样式 */
        .realtime-stats {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            height: 350px;
        }
        .stat-item {
            margin-bottom: 25px;
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        .growth-indicator {
            font-size: 12px;
            margin-left: 5px;
        }
        .growth-up {
            color: #28a745;
        }
        .growth-down {
            color: #dc3545;
        }
        .enhanced-stat-card {
            position: relative;
            overflow: hidden;
        }
        .enhanced-stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(45deg, #1e9fff, #28a745);
        }
        .stat-icon {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 24px;
            opacity: 0.3;
        }
        .layui-tab-brief > .layui-tab-title .layui-this {
            color: #1e9fff;
        }
        
        /* 通道状态统计样式 */
        .channel-stat-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .channel-stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .stat-icon-wrapper {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 20px;
            color: white;
        }
        .stat-icon-wrapper.online { background: linear-gradient(45deg, #28a745, #20c997); }
        .stat-icon-wrapper.total { background: linear-gradient(45deg, #007bff, #0056b3); }
        .stat-icon-wrapper.success { background: linear-gradient(45deg, #28a745, #155724); }
        .stat-icon-wrapper.average { background: linear-gradient(45deg, #ffc107, #e0a800); }
        
        .stat-content .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }
        .stat-content .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        /* 支付排行榜样式 */
        .ranking-list {
            padding: 0;
        }
        .ranking-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.3s ease;
        }
        .ranking-item:hover {
            background: #f8f9fa;
        }
        .ranking-item:last-child {
            border-bottom: none;
        }
        .rank-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 15px;
            font-size: 14px;
        }
        .rank-info {
            flex: 1;
        }
        .rank-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 3px;
        }
        .rank-amount {
            font-size: 12px;
            color: #666;
        }
        .rank-percent {
            font-size: 14px;
            font-weight: 600;
            color: #28a745;
        }
        
        /* 今日数据概览样式 */
        .today-overview {
            padding: 0;
        }
        .overview-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .overview-item:last-child {
            border-bottom: none;
        }
        .overview-label {
            font-size: 13px;
            color: #666;
        }
        .overview-value {
            font-size: 15px;
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
<div class="layuimini-container">
    <div class="layuimini-main">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md8">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6"><!-- layui-col-md12 -->
                        <div class="layui-card">
                            <div class="layui-card-header"><i class="fa fa-warning icon"></i>数据统计</div>
                            <div class="layui-card-body">
                                <div class="welcome-module">
                                    <div class="layui-row layui-col-space10">
                                        <?php foreach($statistics as $k => $v): ?>
                                        <div class="layui-col-xs6"><!-- layui-col-xs3 -->
                                            <div class="panel layui-bg-number enhanced-stat-card">
                                                <div class="panel-body">
                                                    <div class="panel-title">
                                                        <!-- <span class="label pull-right layui-bg-<?php echo htmlentities((string) $v[2]); ?>">实时</span> -->
                                                        <h5><?php echo htmlentities((string) $v[0]); ?></h5>
                                                    </div>
                                                    <div class="panel-content">
                                                        <h1 class="no-margins"><?php echo htmlentities((string) $v[1]); ?></h1>
                                                        <small>当前分类总记录数</small>
                                                        <div class="growth-indicator" id="growth-<?php echo htmlentities((string) $k); ?>">
                                                            <i class="fa fa-arrow-up"></i> 0%
                                                        </div>
                                                    </div>
                                                    <i class="fa <?php 
                                                        $icons = ['fa-money', 'fa-credit-card', 'fa-shopping-cart', 'fa-calendar'];
                                                        echo $icons[$k] ?? 'fa-bar-chart';
                                                     ?> stat-icon"></i>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">
                        <div class="layui-card">
                            <div class="layui-card-header"><i class="fa fa-pie-chart icon icon-blue"></i>通道状态统计</div>
                            <div class="layui-card-body">
                                <div class="welcome-module">
                                    <div class="layui-row layui-col-space10">
                                        <div class="layui-col-xs6">
                                            <div class="channel-stat-item">
                                                <div class="stat-icon-wrapper online">
                                                    <i class="fa fa-check-circle"></i>
                                                </div>
                                                <div class="stat-content">
                                                    <div class="stat-number" id="onlineChannels">0</div>
                                                    <div class="stat-label">在线通道</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs6">
                                            <div class="channel-stat-item">
                                                <div class="stat-icon-wrapper total">
                                                    <i class="fa fa-server"></i>
                                                </div>
                                                <div class="stat-content">
                                                    <div class="stat-number" id="totalChannels">0</div>
                                                    <div class="stat-label">总通道数</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs6">
                                            <div class="channel-stat-item">
                                                <div class="stat-icon-wrapper success">
                                                    <i class="fa fa-thumbs-up"></i>
                                                </div>
                                                <div class="stat-content">
                                                    <div class="stat-number" id="successRateDisplay">0%</div>
                                                    <div class="stat-label">今日成功率</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="layui-col-xs6">
                                            <div class="channel-stat-item">
                                                <div class="stat-icon-wrapper average">
                                                    <i class="fa fa-calculator"></i>
                                                </div>
                                                <div class="stat-content">
                                                    <div class="stat-number" id="avgOrderAmount">¥0</div>
                                                    <div class="stat-label">平均订单</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md12">
                        <div class="layui-card">
                            <div class="layui-card-header">
                                <i class="fa fa-line-chart icon"></i>财务报表分析
                                <div style="float: right;">
                                    <button class="layui-btn layui-btn-xs" id="refreshCharts">
                                        <i class="fa fa-refresh"></i> 刷新
                                    </button>
                                    <button class="layui-btn layui-btn-xs layui-btn-normal" id="exportReport">
                                        <i class="fa fa-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                            <div class="layui-card-body">
                                <!-- 图表标签页 -->
                                <div class="layui-tab layui-tab-brief" lay-filter="chartTabs">
                                    <ul class="layui-tab-title">
                                        <li class="layui-this">趋势分析</li>
                                        <li>支付方式分布</li>
                                        <li>24小时统计</li>
                                        <li>实时监控</li>
                                    </ul>
                                    <div class="layui-tab-content">
                                        <!-- 趋势分析 -->
                                        <div class="layui-tab-item layui-show">
                                            <div id="echarts-trend" style="width: 100%;min-height:400px"></div>
                                        </div>
                                        <!-- 支付方式分布 -->
                                        <div class="layui-tab-item">
                                            <div class="layui-row layui-col-space15">
                                                <div class="layui-col-md6">
                                                    <div id="echarts-payment-pie" style="width: 100%;height:350px"></div>
                                                </div>
                                                <div class="layui-col-md6">
                                                    <div id="echarts-payment-bar" style="width: 100%;height:350px"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 24小时统计 -->
                                        <div class="layui-tab-item">
                                            <div id="echarts-hourly" style="width: 100%;min-height:400px"></div>
                                        </div>
                                        <!-- 实时监控 -->
                                        <div class="layui-tab-item">
                                            <div class="layui-row layui-col-space15">
                                                <div class="layui-col-md8">
                                                    <div id="echarts-realtime" style="width: 100%;height:350px"></div>
                                                </div>
                                                <div class="layui-col-md4">
                                                    <div class="realtime-stats">
                                                        <div class="stat-item">
                                                            <div class="stat-label">实时成功率</div>
                                                            <div class="stat-value" id="realtimeSuccessRate">0%</div>
                                                        </div>
                                                        <div class="stat-item">
                                                            <div class="stat-label">在线通道</div>
                                                            <div class="stat-value" id="realtimeChannels">0/0</div>
                                                        </div>
                                                        <div class="stat-item">
                                                            <div class="stat-label">今日平均</div>
                                                            <div class="stat-value" id="realtimeAverage">¥0</div>
                                                        </div>
                                                        <div class="stat-item">
                                                            <div class="stat-label">最后更新</div>
                                                            <div class="stat-value" id="lastUpdate">--</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md4">

                <div class="layui-card">
                    <div class="layui-card-header"><i class="fa fa-trophy icon icon-tip"></i>支付排行榜</div>
                    <div class="layui-card-body">
                        <div class="ranking-list" id="paymentRanking">
                            <div class="ranking-item">
                                <div class="rank-number">1</div>
                                <div class="rank-info">
                                    <div class="rank-title">支付宝</div>
                                    <div class="rank-amount">¥0</div>
                                </div>
                                <div class="rank-percent">0%</div>
                            </div>
                            <div class="ranking-item">
                                <div class="rank-number">2</div>
                                <div class="rank-info">
                                    <div class="rank-title">微信支付</div>
                                    <div class="rank-amount">¥0</div>
                                </div>
                                <div class="rank-percent">0%</div>
                            </div>
                            <div class="ranking-item">
                                <div class="rank-number">3</div>
                                <div class="rank-info">
                                    <div class="rank-title">其他支付</div>
                                    <div class="rank-amount">¥0</div>
                                </div>
                                <div class="rank-percent">0%</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="layui-card">
                    <div class="layui-card-header"><i class="fa fa-clock-o icon icon-cray"></i>今日数据概览</div>
                    <div class="layui-card-body">
                        <div class="today-overview">
                            <div class="overview-item">
                                <div class="overview-label">订单峰值时段</div>
                                <div class="overview-value" id="peakHour">--:--</div>
                            </div>
                            <div class="overview-item">
                                <div class="overview-label">最大单笔金额</div>
                                <div class="overview-value" id="maxOrder">¥0</div>
                            </div>
                            <div class="overview-item">
                                <div class="overview-label">失败订单数</div>
                                <div class="overview-value" id="failedOrders">0</div>
                            </div>
                            <div class="overview-item">
                                <div class="overview-label">处理时长</div>
                                <div class="overview-value" id="avgProcessTime">0s</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-card">
                    <div class="layui-card-header"><i class="fa fa-paper-plane-o icon"></i>开发团队</div>
                    <div class="layui-card-body layui-text layadmin-text">
                        <table class="layui-table">
                            <colgroup>
                                <col width="100">
                                <col>
                            </colgroup>
                            <tbody>
                                <tr>
                                    <td>当前版本</td>
                                    <td>
                                        <?php echo htmlentities((string) $edition); ?> <button class="layui-btn layui-btn-normal layui-btn-xs" id="update">更新</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>作者QQ</td>
                                    <td>
                                        2352164397
                                    </td>
                                </tr>
                                <tr>
                                    <td>QQ群</td>
                                    <td>
                                        105122659
                                    </td>
                                </tr>
                            <tr>
                                <td>版权所有</td>
                                <td>
                                    www.zzwws.cn <a href="http://www.zzwws.cn/" target="_blank">访问官网</a>
                                </td>
                            </tr>
                            <tr>
                                <td>开发者</td>
                                <td>悠悠楠杉（<EMAIL>）</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<script src="/static/admin/lib/layui-v2.11.4/layui.js" charset="utf-8"></script>
<script src="/static/admin/js/lay-config.js?v=1.0.4" charset="utf-8"></script>
<script>
    layui.use(['layer', 'miniTab','echarts', 'element'], function () {
        var $ = layui.jquery,
            layer = layui.layer,
            miniTab = layui.miniTab,
            echarts = layui.echarts,
            element = layui.element;

        miniTab.listen();
        
        // 监听标签页切换
        element.on('tab(chartTabs)', function(data){
            setTimeout(function() {
                Object.keys(charts).forEach(function(key) {
                    if (charts[key] && typeof charts[key].resize === 'function') {
                        charts[key].resize();
                    }
                });
            }, 100);
        });

        /**
         * 查看公告信息
         **/
        $('body').on('click', '.layuimini-notice', function () {
            var title = $(this).children('.layuimini-notice-title').text(),
                noticeTime = $(this).children('.layuimini-notice-extra').text(),
                content = $(this).children('.layuimini-notice-content').html();
            var html = '<div style="padding:15px 20px; text-align:justify; line-height: 22px;border-bottom:1px solid #2f4056;background-color: #2f4056;color: #ffffff">\n' +
                '<div style="text-align: center;margin-bottom: 20px;font-weight: bold;border-bottom:1px solid #718fb5;padding-bottom: 5px"><h4 class="text-danger">' + title + '</h4></div>\n' +
                '<div style="font-size: 12px">' + content + '</div>\n' +
                '</div>\n';
            parent.layer.open({
                type: 1,
                title: '系统公告'+'<span style="float: right;right: 1px;font-size: 12px;color: #b1b3b9;margin-top: 1px">'+noticeTime+'</span>',
                area: '300px;',
                shade: 0.8,
                id: 'layuimini-notice',
                // btn: ['查看', '取消'],
                btnAlign: 'c',
                moveType: 1,
                content:html,
                shadeClose: true, //开启遮罩关闭
                // success: function (layero) {
                //     var btn = layero.find('.layui-layer-btn');
                //     btn.find('.layui-layer-btn0').attr({
                //         href: 'https://gitee.com/zhongshaofa/layuimini',
                //         target: '_blank'
                //     });
                // }
            });
        });

        // 全局图表对象
        var charts = {};
        
        /**
         * 获取统计数据
         */
        function loadDashboardData() {
            console.log('正在请求数据...');
            // 使用系统内置的API获取数据
            $.ajax({
                url: "<?php echo url("admin/index/data"); ?>",
                type: 'GET',
                data: {},
                dataType: 'json',
                beforeSend: function(xhr) {
                    console.log('发送请求前，URL: <?php echo url("admin/index/data"); ?>');
                    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                },
                success: function(res) {
                    console.log('收到响应:', res);
                    console.log('响应类型:', typeof res);
                    
                    if(res && res.code === 0){
                        console.log('数据对象:', res.data);
                        
                        // 直接更新DOM元素
                        console.log('直接更新DOM元素...');
                        document.getElementById('onlineChannels').innerText = res.data.onlineChannels || 0;
                        document.getElementById('totalChannels').innerText = res.data.activeChannels || 0;
                        document.getElementById('successRateDisplay').innerText = (res.data.successRate || 0) + '%';
                        
                        var avgAmount = res.data.order2 > 0 ? (res.data.income2 / res.data.order2).toFixed(2) : 0;
                        document.getElementById('avgOrderAmount').innerText = '¥' + avgAmount;
                        
                        // 更新数据统计卡片
                        console.log('更新数据统计卡片...');
                        updateStatisticsCards(res.data);
                        
                        // 更新支付排行榜
                        console.log('更新支付排行榜...');
                        console.log('支付排行榜数据:', res.data.payTypeRanking);
                        var ranking = res.data.payTypeRanking || [];
                        var rankingHtml = '';
                        
                        // 确保ranking是数组
                        if (!Array.isArray(ranking)) {
                            console.error('支付排行榜数据不是数组:', ranking);
                            ranking = [];
                        }
                        
                        ranking.forEach(function(item) {
                            rankingHtml += '<div class="ranking-item">' +
                                '<div class="rank-number">' + item.rank + '</div>' +
                                '<div class="rank-info">' +
                                    '<div class="rank-title">' + item.name + '</div>' +
                                    '<div class="rank-amount">¥' + parseFloat(item.amount || 0).toLocaleString() + '</div>' +
                                '</div>' +
                                '<div class="rank-percent">' + (item.percent || 0) + '%</div>' +
                            '</div>';
                        });
                        
                        // 如果没有数据，显示默认的排行榜
                        if (rankingHtml === '') {
                            rankingHtml = '<div class="ranking-item">' +
                                '<div class="rank-number">1</div>' +
                                '<div class="rank-info">' +
                                    '<div class="rank-title">暂无数据</div>' +
                                    '<div class="rank-amount">¥0</div>' +
                                '</div>' +
                                '<div class="rank-percent">0%</div>' +
                            '</div>' +
                            '<div class="ranking-item">' +
                                '<div class="rank-number">2</div>' +
                                '<div class="rank-info">' +
                                    '<div class="rank-title">暂无数据</div>' +
                                    '<div class="rank-amount">¥0</div>' +
                                '</div>' +
                                '<div class="rank-percent">0%</div>' +
                            '</div>' +
                            '<div class="ranking-item">' +
                                '<div class="rank-number">3</div>' +
                                '<div class="rank-info">' +
                                    '<div class="rank-title">暂无数据</div>' +
                                    '<div class="rank-amount">¥0</div>' +
                                '</div>' +
                                '<div class="rank-percent">0%</div>' +
                            '</div>';
                        }
                        
                        document.getElementById('paymentRanking').innerHTML = rankingHtml;
                        console.log('支付排行榜HTML:', rankingHtml);
                        
                        // 更新今日数据概览
                        console.log('更新今日数据概览...');
                        var peakHourText = res.data.peakHour ?
                            (res.data.peakHour < 10 ? '0' + res.data.peakHour : res.data.peakHour) + ':00' :
                            '--:--';
                        document.getElementById('peakHour').innerText = peakHourText;
                        
                        document.getElementById('maxOrder').innerText = '¥' + parseFloat(res.data.maxOrderToday || 0).toLocaleString();
                        document.getElementById('failedOrders').innerText = parseInt(res.data.failedOrdersToday || 0).toLocaleString();
                        document.getElementById('avgProcessTime').innerText = (res.data.avgProcessTime || 0) + 's';
                        
                        console.log('DOM元素更新完成');
                        
                        // 初始化图表
                        console.log('初始化图表...');
                        setTimeout(function() {
                            initTrendChart(res.data);
                            initPaymentDistribution(res.data);
                            initHourlyChart(res.data);
                            initRealtimeChart(res.data);
                            
                            // 更新实时统计
                            updateRealtimeStats(res.data);
                            
                            console.log('图表初始化完成');
                        }, 100);
                        layer.msg('数据加载成功', {icon: 1});
                    } else {
                        console.error('数据请求失败:', res ? res.msg : '未知错误');
                        layer.msg('数据加载失败: ' + (res ? res.msg : '未知错误'), {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX请求失败:', status, error);
                    console.error('响应状态:', xhr.status);
                    console.error('响应文本:', xhr.responseText);
                    layer.msg('网络错误，请稍后重试', {icon: 2});
                }
            });
        }

        /**
         * 更新数据统计卡片
         */
        function updateStatisticsCards(data) {
            console.log('更新数据统计卡片函数被调用，数据:', data);
            
            // 获取所有统计卡片中的数值元素
            var statCards = document.querySelectorAll('.panel-content h1.no-margins');
            console.log('找到统计卡片数量:', statCards.length);
            
            // 更新第一个卡片：总收入
            if (statCards[0]) {
                statCards[0].innerText = '¥' + parseFloat(data.income1 || 0).toLocaleString();
                console.log('更新总收入:', data.income1);
            }
            
            // 更新第二个卡片：今日收入
            if (statCards[1]) {
                statCards[1].innerText = '¥' + parseFloat(data.income2 || 0).toLocaleString();
                console.log('更新今日收入:', data.income2);
            }
            
            // 更新第三个卡片：总订单数
            if (statCards[2]) {
                statCards[2].innerText = parseInt(data.order1 || 0).toLocaleString();
                console.log('更新总订单数:', data.order1);
            }
            
            // 更新第四个卡片：今日订单数
            if (statCards[3]) {
                statCards[3].innerText = parseInt(data.order2 || 0).toLocaleString();
                console.log('更新今日订单数:', data.order2);
            }
            
            // 更新增长率指示器
            updateGrowthIndicators(data);
            
            console.log('数据统计卡片更新完成');
        }

        /**
         * 更新增长率指示器
         */
        function updateGrowthIndicators(data) {
            console.log('更新增长率指示器');
            
            // 计算增长率（这里使用简单的比较，实际可能需要更复杂的计算）
            var incomeGrowth = data.income1 > 0 ? ((data.income2 / data.income1) * 100 - 100).toFixed(1) : 0;
            var orderGrowth = data.order1 > 0 ? ((data.order2 / data.order1) * 100 - 100).toFixed(1) : 0;
            
            // 更新收入增长率
            updateGrowthIndicator(0, incomeGrowth);
            
            // 更新订单增长率
            updateGrowthIndicator(2, orderGrowth);
            
            console.log('增长率指示器更新完成');
        }

        /**
         * 更新单个增长率指示器
         */
        function updateGrowthIndicator(index, growth) {
            var $indicator = $('#growth-' + index);
            var isPositive = growth >= 0;
            var icon = isPositive ? 'fa-arrow-up' : 'fa-arrow-down';
            var className = isPositive ? 'growth-up' : 'growth-down';
            
            $indicator.html('<i class="fa ' + icon + '"></i> ' + Math.abs(growth) + '%')
                     .removeClass('growth-up growth-down')
                     .addClass(className);
        }

        /**
         * 初始化趋势分析图表
         */
        function initTrendChart(data) {
            console.log('初始化趋势分析图表，数据:', data);
            
            if (charts.trend) {
                charts.trend.dispose();
            }
            
            charts.trend = echarts.init(document.getElementById('echarts-trend'), 'walden');
            
            // 确保数据存在
            var chartData = data.chartData || {};
            var days = chartData.days || [];
            var income = chartData.income || [];
            var orders = chartData.orders || [];
            
            console.log('图表数据:', {days: days, income: income, orders: orders});
            
            var option = {
                title: {
                    text: '7日收入趋势',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    data: ['收入', '订单数'],
                    bottom: 0
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '8%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: days
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '金额(元)',
                        position: 'left',
                        axisLabel: {
                            formatter: '¥{value}'
                        }
                    },
                    {
                        type: 'value',
                        name: '订单数',
                        position: 'right'
                    }
                ],
                series: [
                    {
                        name: '收入',
                        type: 'line',
                        smooth: true,
                        data: income,
                        itemStyle: {
                            color: '#1e9fff'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(30, 159, 255, 0.5)' },
                                { offset: 1, color: 'rgba(30, 159, 255, 0.1)' }
                            ])
                        }
                    },
                    {
                        name: '订单数',
                        type: 'bar',
                        yAxisIndex: 1,
                        data: orders,
                        itemStyle: {
                            color: '#28a745'
                        }
                    }
                ]
            };
            
            console.log('设置图表选项:', option);
            charts.trend.setOption(option);
            console.log('趋势分析图表初始化完成');
        }

        /**
         * 初始化支付方式分布图表
         */
        function initPaymentDistribution(data) {
            console.log('初始化支付方式分布图表，数据:', data);
            
            // 确保数据存在
            var payTypeStats = data.payTypeStats || [];
            console.log('支付方式统计数据:', payTypeStats);
            
            // 如果没有数据，创建默认数据
            if (!Array.isArray(payTypeStats) || payTypeStats.length === 0) {
                console.warn('支付方式统计数据为空，使用默认数据');
                payTypeStats = [
                    { type: 1, type_name: '微信支付', count: 0, amount: 0, channels: [] },
                    { type: 2, type_name: '支付宝', count: 0, amount: 0, channels: [] },
                    { type: 3, type_name: 'QQ支付', count: 0, amount: 0, channels: [] },
                    { type: 4, type_name: '数字人民币', count: 0, amount: 0, channels: [] }
                ];
            }
            
            // 确保所有支付方式都存在，即使没有数据
            var allPayTypes = [
                { type: 1, type_name: '微信支付' },
                { type: 2, type_name: '支付宝' },
                { type: 3, type_name: 'QQ支付' },
                { type: 4, type_name: '数字人民币' }
            ];
            
            // 检查哪些支付方式缺失
            var existingTypes = payTypeStats.map(item => item.type);
            console.log('已存在的支付方式类型:', existingTypes);
            
            // 添加缺失的支付方式
            allPayTypes.forEach(function(defaultType) {
                if (!existingTypes.includes(defaultType.type)) {
                    console.log('添加缺失的支付方式:', defaultType.type_name);
                    payTypeStats.push({
                        type: defaultType.type,
                        type_name: defaultType.type_name,
                        count: 0,
                        amount: 0,
                        channels: []
                    });
                }
            });
            
            // 确保数据是有效的
            payTypeStats = payTypeStats.map(function(item) {
                return {
                    type: item.type || 0,
                    type_name: item.type_name || '未知',
                    count: parseInt(item.count || 0),
                    amount: parseFloat(item.amount || 0),
                    channels: item.channels || []
                };
            });
            
            console.log('处理后的支付方式统计数据:', payTypeStats);
            console.log('支付方式统计数据数量:', payTypeStats.length);
            
            // 饼图
            if (charts.paymentPie) {
                charts.paymentPie.dispose();
            }
            
            charts.paymentPie = echarts.init(document.getElementById('echarts-payment-pie'), 'walden');
            var pieData = payTypeStats.map(function(item) {
                return {
                    name: item.type_name,
                    value: parseFloat(item.amount || 0),
                    count: parseInt(item.count || 0),
                    channels: item.channels || []
                };
            });
            
            console.log('饼图数据:', pieData);
            console.log('饼图数据数量:', pieData.length);
            
            // 确保所有支付方式都显示，即使金额为0
            // 检查是否有金额全为0的情况
            var totalAmount = pieData.reduce(function(sum, item) {
                return sum + item.value;
            }, 0);
            
            console.log('总金额:', totalAmount);
            
            // 如果总金额为0，为每个支付方式设置一个最小值，确保饼图能显示
            if (totalAmount === 0) {
                console.log('所有支付方式金额都为0，设置最小值确保显示');
                pieData = pieData.map(function(item, index) {
                    return {
                        name: item.name,
                        value: 0.01, // 设置一个最小值
                        count: item.count,
                        channels: item.channels,
                        originalValue: 0 // 保存原始值
                    };
                });
            }
            
            var pieOption = {
                title: {
                    text: '支付方式金额分布',
                    left: 'center',
                    top: 20
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        console.log('饼图tooltip参数:', params);
                        // 查找对应的支付模式数据
                        var payType = payTypeStats.find(item => item.type_name === params.name);
                        console.log('找到的支付类型数据:', payType);
                        
                        // 获取正确的金额值（如果设置了最小值，则使用原始值）
                        var displayAmount = params.data.originalValue !== undefined ? params.data.originalValue : params.value;
                        
                        var channelsInfo = '';
                        if (payType && payType.channels && payType.channels.length > 0) {
                            // channelsInfo = '<br/>通道详情: ';
                            payType.channels.forEach(function(channel) {
                                channelsInfo += '<br/>' + channel.title + ': ¥' + parseFloat(channel.amount || 0).toLocaleString() + ' (' + channel.count + '笔)';
                            });
                        }
                        
                        // 如果金额为0，显示特殊文本
                        var amountText = displayAmount === 0 ? '¥0 (无交易)' : '¥' + displayAmount.toLocaleString();
                        
                        // 使用完整的格式：{a} {b} （{d}%）
                        return params.seriesName + ' ' + params.name + ' （' + params.percent + '%）<br/>金额: ' + amountText + channelsInfo;
                    }
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    top: 'middle',
                    formatter: function(name) {
                        console.log('饼图legend格式化，名称:', name);
                        // 查找对应的支付模式数据
                        var payType = payTypeStats.find(item => item.type_name === name);
                        console.log('找到的支付类型数据:', payType);
                        
                        // 确保所有支付方式都显示，即使没有数据
                        if (payType) {
                            if (payType.channels && payType.channels.length > 0) {
                                var legendText = name + ' (' + payType.channels.length + '个通道)';
                                console.log('生成的legend文本:', legendText);
                                return legendText;
                            } else {
                                var legendText = name + ' (无通道)';
                                console.log('生成的legend文本:', legendText);
                                return legendText;
                            }
                        }
                        
                        console.log('返回的名称:', name);
                        return name;
                    }
                },
                series: [
                    {
                        name: '',// 支付金额
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['60%', '55%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: pieData
                    }
                ]
            };
            charts.paymentPie.setOption(pieOption);
            console.log('饼图初始化完成');

            // 柱状图
            if (charts.paymentBar) {
                charts.paymentBar.dispose();
            }
            
            charts.paymentBar = echarts.init(document.getElementById('echarts-payment-bar'), 'walden');
            var barData = payTypeStats || [];
            
            console.log('柱状图数据:', barData);
            console.log('柱状图数据数量:', barData.length);
            
            var barOption = {
                title: {
                    text: '支付方式订单量',
                    left: 'center',
                    top: 20
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        console.log('柱状图tooltip参数:', params);
                        var result = params[0].name + '<br/>';
                        params.forEach(function(item) {
                            console.log('柱状图tooltip项:', item);
                            // 查找对应的支付模式数据
                            var payType = payTypeStats.find(p => p.type_name === item.axisValue);
                            console.log('找到的支付类型数据:', payType);
                            var channelsInfo = '';
                            if (payType && payType.channels && payType.channels.length > 0) {
                                // channelsInfo = '<br/>通道详情: ';
                                payType.channels.forEach(function(channel) {
                                    channelsInfo += '<br/>' + channel.title + ': ' + channel.count + '笔';
                                });
                            }
                            
                            // 如果订单数为0，显示特殊文本
                            var orderText = item.value === 0 ? '0 (无订单)' : item.value;
                            
                            result += item.marker + item.seriesName + ': ' + orderText + channelsInfo + '<br/>';
                        });
                        return result;
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: barData.map(function(item) { return item.type_name; }),
                    axisLabel: {
                        rotate: 45,
                        interval: 0,
                        margin: 15,
                        textStyle: {
                            fontSize: 12
                        }
                    }
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '订单数',
                        type: 'bar',
                        data: barData.map(function(item) { return item.count || 0; }),
                        itemStyle: {
                            color: function(params) {
                                var colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];
                                return colors[params.dataIndex % colors.length];
                            }
                        }
                    }
                ]
            };
            charts.paymentBar.setOption(barOption);
            console.log('柱状图初始化完成');
        }

        /**
         * 初始化24小时统计图表
         */
        function initHourlyChart(data) {
            if (charts.hourly) {
                charts.hourly.dispose();
            }
            
            charts.hourly = echarts.init(document.getElementById('echarts-hourly'), 'walden');
            var hourlyData = data.hourlyStats || [];
            
            var option = {
                title: {
                    text: '今日24小时交易统计',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    data: ['订单数', '交易金额'],
                    bottom: 0
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '8%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: hourlyData.map(function(item) { return item.hour; })
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '订单数',
                        position: 'left'
                    },
                    {
                        type: 'value',
                        name: '金额(元)',
                        position: 'right',
                        axisLabel: {
                            formatter: '¥{value}'
                        }
                    }
                ],
                series: [
                    {
                        name: '订单数',
                        type: 'bar',
                        yAxisIndex: 0,
                        data: hourlyData.map(function(item) { return item.orders; })
                    },
                    {
                        name: '交易金额',
                        type: 'line',
                        yAxisIndex: 1,
                        data: hourlyData.map(function(item) { return item.amount; }),
                        smooth: true
                    }
                ]
            };
            charts.hourly.setOption(option);
        }

        /**
         * 初始化实时监控图表
         */
        function initRealtimeChart(data) {
            console.log('初始化实时监控图表，数据:', data);
            
            // 确保DOM元素存在
            var chartDom = document.getElementById('echarts-realtime');
            if (!chartDom) {
                console.error('实时监控图表DOM元素不存在');
                return;
            }
            
            // 如果图表已存在，先销毁
            if (charts.realtime) {
                try {
                    charts.realtime.dispose();
                } catch (e) {
                    console.error('销毁图表时出错:', e);
                }
            }
            
            // 初始化图表
            try {
                charts.realtime = echarts.init(chartDom, 'walden');
                console.log('ECharts实例创建成功');
            } catch (e) {
                console.error('创建ECharts实例失败:', e);
                return;
            }
            
            // 获取成功率数据
            var successRate = parseFloat(data.successRate) || 0;
            console.log('成功率数据:', successRate);
            
            // 使用简单的饼图代替仪表盘
            var option = {
                title: {
                    text: '实时交易监控',
                    left: 'center',
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    formatter: '{a} <br/>{b}: {c}% ({d}%)'
                },
                series: [
                    {
                        name: '交易状态',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['50%', '50%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: true,
                            position: 'outside',
                            formatter: '{b}: {c}%'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '16',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: true
                        },
                        data: [
                            {value: successRate, name: '成功率', itemStyle: {color: '#28a745'}},
                            {value: 100 - successRate, name: '其他', itemStyle: {color: '#e9ecef'}}
                        ]
                    }
                ]
            };
            
            console.log('设置实时监控图表选项:', option);
            
            try {
                charts.realtime.setOption(option);
                console.log('实时监控图表初始化完成');
            } catch (e) {
                console.error('设置图表选项失败:', e);
            }
            
            // 手动触发一次resize，确保图表正确显示
            setTimeout(function() {
                try {
                    if (charts.realtime) {
                        charts.realtime.resize();
                        console.log('图表resize完成');
                    }
                } catch (e) {
                    console.error('图表resize失败:', e);
                }
            }, 100);
        }

                 /**
                  * 更新实时统计
                  */
                 function updateRealtimeStats(data) {
                     console.log('更新实时统计...');
                     $('#realtimeSuccessRate').text((data.successRate || 0) + '%');
                     $('#realtimeChannels').text((data.onlineChannels || 0) + '/' + (data.activeChannels || 0));
                     var avgAmount = data.order2 > 0 ? (data.income2 / data.order2).toFixed(2) : 0;
                     $('#realtimeAverage').text('¥' + avgAmount);
                     $('#lastUpdate').text(new Date().toLocaleTimeString());
                     
                     // 更新通道状态统计
                     console.log('更新通道状态统计...');
                     updateChannelStats(data);
                     
                     // 更新支付排行榜
                     console.log('更新支付排行榜...');
                     updatePaymentRanking(data);
                     
                     // 更新今日数据概览
                     console.log('更新今日数据概览...');
                     updateTodayOverview(data);
                     console.log('实时统计更新完成');
                 }

         /**
          * 更新通道状态统计
          */
         function updateChannelStats(data) {
             console.log('更新通道状态统计函数被调用，数据:', data);
             $('#onlineChannels').text(data.onlineChannels || 0);
             $('#totalChannels').text(data.activeChannels || 0);
             $('#successRateDisplay').text((data.successRate || 0) + '%');
             
             var avgAmount = data.order2 > 0 ? (data.income2 / data.order2) : 0;
             $('#avgOrderAmount').text('¥' + avgAmount.toFixed(2));
             console.log('通道状态统计更新完成');
         }

         /**
          * 更新支付排行榜
          */
         function updatePaymentRanking(data) {
             console.log('更新支付排行榜函数被调用，数据:', data);
             var ranking = data.payTypeRanking || [];
             var $ranking = $('#paymentRanking');
             
             console.log('支付排行榜数据:', ranking);
             $ranking.empty();
             
             // 确保ranking是数组
             if (!Array.isArray(ranking)) {
                 console.error('支付排行榜数据不是数组:', ranking);
                 ranking = [];
             }
             
             // 如果没有数据，显示默认的排行榜
             if (ranking.length === 0) {
                 console.warn('支付排行榜数据为空，使用默认数据');
                 var defaultRanking = [
                     { rank: 1, name: '暂无数据', amount: 0, percent: 0 },
                     { rank: 2, name: '暂无数据', amount: 0, percent: 0 },
                     { rank: 3, name: '暂无数据', amount: 0, percent: 0 }
                 ];
                 ranking = defaultRanking;
             }
             
             // 确保数据是有效的
             ranking = ranking.map(function(item) {
                 return {
                     rank: parseInt(item.rank || 0),
                     name: item.name || '未知',
                     amount: parseFloat(item.amount || 0),
                     percent: parseFloat(item.percent || 0)
                 };
             });
             
             console.log('处理后的支付排行榜数据:', ranking);
             
             ranking.forEach(function(item, index) {
                 var rankHtml = '<div class="ranking-item">' +
                     '<div class="rank-number">' + item.rank + '</div>' +
                     '<div class="rank-info">' +
                         '<div class="rank-title">' + item.name + '</div>' +
                         '<div class="rank-amount">¥' + item.amount.toLocaleString() + '</div>' +
                     '</div>' +
                     '<div class="rank-percent">' + item.percent + '%</div>' +
                 '</div>';
                 $ranking.append(rankHtml);
                 console.log('添加排行榜项:', rankHtml);
             });
             console.log('支付排行榜更新完成');
         }

         /**
          * 更新今日数据概览
          */
         function updateTodayOverview(data) {
             console.log('更新今日数据概览函数被调用，数据:', data);
             var peakHourText = data.peakHour ?
                 (data.peakHour < 10 ? '0' + data.peakHour : data.peakHour) + ':00' :
                 '--:--';
             $('#peakHour').text(peakHourText);
             
             $('#maxOrder').text('¥' + parseFloat(data.maxOrderToday || 0).toLocaleString());
             $('#failedOrders').text(parseInt(data.failedOrdersToday || 0).toLocaleString());
             $('#avgProcessTime').text((data.avgProcessTime || 0) + 's');
             console.log('今日数据概览更新完成');
         }

        // 初始化加载数据
        loadDashboardData();

        // 定时刷新（每5分钟）
        setInterval(loadDashboardData, 300000);

        // 刷新按钮
        $('#refreshCharts').click(function() {
            loadDashboardData();
            layer.msg('数据已刷新', {icon: 1});
        });

        // 导出报表
        $('#exportReport').click(function() {
            window.location.href = "<?php echo url("admin/index/exportReport"); ?>";
        });

        // echarts 窗口缩放自适应
        window.onresize = function(){
            Object.keys(charts).forEach(function(key) {
                if (charts[key] && typeof charts[key].resize === 'function') {
                    charts[key].resize();
                }
            });
        }
        
        // 检查更新，每小时一次
        $.post("<?php echo url("index/checkUpdate"); ?>",function (data) {
            if (data.code==1) {
                layer.confirm(data.msg, {
                    btn: ['去看看','立即更新','算啦']
                }, function(){
                    window.open(data.data);
                }, function(){
                    var loadIndex = layer.msg('正在处理,请稍候...', {
                        icon: 16,
                        shade: 0.01
                    });
                    $.post("<?php echo url("index/update"); ?>",function(res){
                        layer.close(loadIndex);
                        var icon = res.code === 1 ? 1 : 2;
                        layer.msg(res.msg,{icon: icon},function(){
                            if(res.code === 1){
                                location.reload();
                            }
                        });
                    })
                });
            }
        },'json');
        
        // 更新
        $('#update').on('click',function(){
            var loadIndex = layer.msg('正在处理,请稍候...', {
                icon: 16,
                shade: 0.01
            });
            $.post("<?php echo url("index/update"); ?>",function(res){
                layer.close(loadIndex);
                var icon = res.code === 1 ? 1 : 2;
                layer.msg(res.msg,{icon: icon},function(){
                    if(res.code === 1){
                        location.reload();
                    }
                });
            })
        })
    });
</script>
</body>
</html>
