<?php
require_once 'extend/callback/WXBizMsgCrypt.php';
require_once 'extend/callback/ErrorCode.php';
require_once 'extend/callback/Prpcrypt.php';
require_once 'extend/callback/XMLParse.php';
require_once 'extend/callback/SHA1.php';
require_once 'extend/callback/PKCS7Encoder.php';

use callback\WXBizMsgCrypt;
use callback\ErrorCode;
use callback\Prpcrypt;
use callback\XMLParse;
use callback\SHA1;
use callback\PKCS7Encoder;

// 从日志中获取的真实数据
$xmlData = '<xml><ToUserName><![CDATA[ww666a49fdd2b8622e]]></ToUserName><Encrypt><![CDATA[ZY/42jihKIHDrUV7yCAHg0QkxR8BGTXQlarCef2s0X+NfkR/9pA/gp9nK9rWBzY/m9FVXyL5j4FozOTebW9XCVo1cOE/Mw7PeCLJY1K3NZXs8akiHLrFI4IjK1Uo29YFyK6EscBoI2sE01v48BTBhrAj7Nrug3wHgvmqNme+VCiXmS4b6mNXIxf8jQu8pfxPZ7HQG+RB2WlgzRK2QbQ7GSWIO747dyim5waa9W3cPmflnUvpDwAUZT8RHk8mSAzHwERVQxMJ6VSBumbj3E9U1F3D2lnPRlHbwvTOWIJ/lNMI9Ctak9o4Vi0dL4aErauv//XWj0P0aVlal4FtmgmYslc6e24AX0PkraqdVtrsL5y8Hny28hSIW4IA/XENgpY7pZCy3koGnViHpq5nQgGGqRt+sogrjTGZWddNBdHeGT/2KbERR9X+04kDisTAjIB9ge9XYWO6PM5gyOhkJ/GDSl2a5TPyvtE0GsJLSpXYqz/7cq9ZEekCGQYxicGJyrPK]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>';

// 从日志中获取的参数（这些是模拟的，因为日志中没有提供）
$msgSignature = "test_signature"; // 实际值需要从请求中获取
$timestamp = "1724208721"; // 从日志时间戳推断
$nonce = "test_nonce"; // 实际值需要从请求中获取

echo "测试完整的企业微信消息解密过程\n";
echo "XML数据: " . $xmlData . "\n";
echo "时间戳: " . $timestamp . "\n";
echo "签名: " . $msgSignature . "\n";
echo "随机数: " . $nonce . "\n\n";

// 直接使用企业微信配置（从数据库中获取的正确值）
$token = "XjQLm4iGs8YtmGQ3oE9Q0Op";
$encodingAesKey = "X198BDkRlpg375z4hwakFKwp8MgyLa7GsRleDi8WgVw";
$corpId = "ww666a49fdd2b8622e";

echo "企业微信配置:\n";
echo "Token: " . $token . "\n";
echo "EncodingAESKey: " . $encodingAesKey . "\n";
echo "CorpId: " . $corpId . "\n\n";

// 创建WXBizMsgCrypt实例
$wxCrypt = new WXBizMsgCrypt($token, $encodingAesKey, $corpId);

// 测试解密过程
$decryptMsg = "";
$errCode = $wxCrypt->DecryptMsg($msgSignature, $timestamp, $nonce, $xmlData, $decryptMsg);

echo "解密结果:\n";
echo "错误码: " . $errCode . "\n";

if ($errCode === 0) {
    echo "解密成功!\n";
    echo "解密后的消息: " . $decryptMsg . "\n";
} else {
    echo "解密失败，错误码: " . $errCode . "\n";
    
    // 检查错误码含义
    switch ($errCode) {
        case ErrorCode::$OK:
            echo "错误类型: 成功\n";
            break;
        case ErrorCode::$ValidateSignatureError:
            echo "错误类型: 签名验证错误 (-40001)\n";
            break;
        case ErrorCode::$ParseXmlError:
            echo "错误类型: XML解析错误 (-40002)\n";
            break;
        case ErrorCode::$ComputeSignatureError:
            echo "错误类型: 签名计算错误 (-40003)\n";
            break;
        case ErrorCode::$IllegalAesKey:
            echo "错误类型: 非法AES密钥 (-40004)\n";
            break;
        case ErrorCode::$IllegalBuffer:
            echo "错误类型: 非法缓冲区 (-40005)\n";
            break;
        case ErrorCode::$DecodeBase64Error:
            echo "错误类型: Base64解码错误 (-40006)\n";
            break;
        default:
            echo "错误类型: 未知错误\n";
            break;
    }
}

// 测试第二个XML数据
$xmlData2 = '<xml><ToUserName><![CDATA[ww666a49fdd2b8622e]]></ToUserName><Encrypt><![CDATA[nR8hVEbd+5KPygZesJeQquBHu+7gbH+sR37Ov6rmr70avgVERlvGGm+4Ftg36WFAbWuQlWbyebQQvCtH3ufZfvQkhHgjT7w/ewE41qMRS0J0AZDR/KMEpFUfDEofc700EAENiOQ8hb+lIz58dxgKHw/tlNrhCXxnw/vDyBgFlPI6zigkZ5Rhtd6z2YUqalTVhG/TP8GM3fcUCQJwsmzzydzSxHXbSKYYMfAGM9LI3EZdMsEOfRs7S4b+PFu7PeiLI5w9DUVCh2otINCBLc1z++TzzGwe9wY+zdcYyL5f9pGXHOlE3cixKOTdh4iuIMzPtvOyXA74QgqaNRsCqzptuApIjuC9fSA/cvbTAQ8Ye6/+ffJP4TLJdHoIFPW++lS7elGRrCimvdR9n/cXduSPJEWCnIqMVPd6GPYOGysE2IRf7M37sEw9BS5zhcxXzGp/W+HCHEvleFhSM30fpKXvvs2BQ3Xv6TIF2ag9DQINJOrZ3oIjap1aYoRxT1CidJfJ]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>';

echo "\n\n测试第二个XML数据\n";
echo "XML数据: " . $xmlData2 . "\n\n";

$decryptMsg2 = "";
$errCode2 = $wxCrypt->DecryptMsg($msgSignature, $timestamp, $nonce, $xmlData2, $decryptMsg2);

echo "解密结果:\n";
echo "错误码: " . $errCode2 . "\n";

if ($errCode2 === 0) {
    echo "解密成功!\n";
    echo "解密后的消息: " . $decryptMsg2 . "\n";
} else {
    echo "解密失败，错误码: " . $errCode2 . "\n";
}