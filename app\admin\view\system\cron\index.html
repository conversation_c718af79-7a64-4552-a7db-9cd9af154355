<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>定时任务</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="__STATIC__/lib/layui-v2.11.4/css/layui.css" media="all">
    <link rel="stylesheet" href="__STATIC__/css/public.css" media="all">
</head>

<body>
    <div class="layuimini-container">
        <div class="layuimini-main" id="app">
            <div class="layui-card">
                <div class="layui-card-header">
                    定时任务
                    <!--<button id="cronStatus" class="layui-btn layui-btn-xs layui-btn-primary">检测中...</button>-->
                </div>
                <div class="layui-card-body ">
                    <blockquote class="site-text layui-elem-quote">
                        <span id="show_code"
                            style="font-size: 15px;font-family:'Helvetica Neue, Helvetica, Arial, sans-serif';">php82 {$root}think cron</span>
                    </blockquote>
                    <strong>宝塔"计划任务"监控步骤 计划任务->Shell脚本->执行周期改成"1分钟"执行一次->然后脚本内容就是以上<br>
                        推送任务：
                        1、同步在线状态
                        2、清理7天前过期订单
                        3、关闭过期订单
                        4、回调失败订单再次执行
                    </strong>
                </div>
            </div>
            <br>
            <div class="layui-card">
                <div class="layui-card-header">
                    消息队列
                    <!--<button id="queueStatus" class="layui-btn layui-btn-xs layui-btn-primary">检测中...</button>-->
                </div>
                <div class="layui-card-body ">
                    <blockquote class="site-text layui-elem-quote">
                        <span id="show_code"
                            style="font-size: 15px;font-family:'Helvetica Neue, Helvetica, Arial, sans-serif';">php82 think queue:listen</span>
                    </blockquote>
                    <strong>用于异步执行推送任务，使用Supervisor（进程守护管理器）监控步骤：<br>
                        名称：随便填写<br>
                        运行目录：{$root}<br>
                        启动命令：php82 think queue:listen
                    </strong>
                </div>
            </div>
            <br>
            <div class="layui-card">
                <div class="layui-card-header">
                    定时任务错误日志
                    <button class="layui-btn layui-btn-xs layui-btn-danger" style="float: right;" id="clearErrors">清除日志</button>
                </div>
                <div class="layui-card-body">
                    {if empty($cronErrors)}
                    <div class="layui-text" style="text-align: center; color: #999; padding: 20px 0;">
                        暂无错误日志
                    </div>
                    {else /}
                    <textarea class="layui-textarea" style="height: 300px; font-family: monospace; font-size: 12px; line-height: 1.5;" readonly>{volist name="cronErrors" id="error"}
[{$error.time}] {$error.job}: {$error.message}  
{/volist}</textarea>
                    <div style="margin-top: 10px; color: #999; font-size: 12px;">
                        共 {:count($cronErrors)} 条错误记录，日志有效期1天
                    </div>
                    {/if}
                </div>
            </div>
            <br>
        </div>
    </div>

    <script src="__STATIC__/lib/layui-v2.11.4/layui.js" charset="utf-8"></script>
    <script>
        layui.use(['form', 'layer'], function () {
            var form = layui.form,
                layer = layui.layer,
                $ = layui.$;

            // 页面加载完成后立即检测一次状态
            // checkServiceStatus();
            
            // 每30秒检测一次服务状态
            // setInterval(checkServiceStatus, 30000);

            // 检查服务状态函数
            function checkServiceStatus() {
                $.get("{:url('system.cron/checkStatus')}", function(res){
                    if(res.code == 1){
                        // 更新定时任务状态
                        updateStatusBadge('cronStatus', res.data.cron);
                        
                        // 更新消息队列状态
                        updateStatusBadge('queueStatus', res.data.queue);
                    }
                });
            }
            
            // 更新状态按钮函数
            function updateStatusBadge(elementId, isRunning) {
                var $element = $('#' + elementId);
                
                if(isRunning){
                    $element.removeClass('layui-btn-primary').addClass('layui-btn-normal');
                    $element.text('运行中');
                } else {
                    $element.removeClass('layui-btn-normal').addClass('layui-btn-primary');
                    $element.text('未运行');
                }
            }

            //监听提交
            form.on('submit(saveBtn)', function (data) {
                $.post("{:url('system.config/edit')}", data.field, function (res) {
                    icon = res.code == 1 ? 1 : 2;
                    layer.msg(res.msg, { time: 1500, icon: icon }, function () {
                        if (res.code == 1) {
                            location.reload();
                        }
                    })
                })

                return false;
            });
            
            // 清除错误日志
            $('#clearErrors').on('click', function(){
                layer.confirm('确定要清除所有错误日志吗？', {
                    btn: ['确定','取消']
                }, function(){
                    $.post("{:url('system.cron/clearErrors')}", function(res){
                        if(res.code == 1){
                            layer.msg(res.msg, {icon: 1});
                            setTimeout(function(){
                                location.reload();
                            }, 1500);
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    });
                });
            });

        });
    </script>
</body>

</html>
