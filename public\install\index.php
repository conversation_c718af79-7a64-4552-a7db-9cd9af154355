<?php
$title = '至尊码支付';

ini_set('display_errors', 'On');
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

use think\facade\Db;

define('DS', DIRECTORY_SEPARATOR);
define('ROOT_PATH', dirname($_SERVER['DOCUMENT_ROOT']) . DS);
define('INSTALL_PATH', ROOT_PATH . 'public' . DS . 'install' . DS);

require ROOT_PATH . 'vendor/autoload.php';
require ROOT_PATH . 'vendor/topthink/framework/src/helper.php';

$currentHost = ($_SERVER['SERVER_PORT'] == 443 ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . '/';

function isReadWrite($file)
{
    if (DIRECTORY_SEPARATOR == '\\') {
        return true;
    }
    if (DIRECTORY_SEPARATOR == '/' && @ ini_get("safe_mode") === false) {
        return is_writable($file);
    }
    if (!is_file($file) || ($fp = @fopen($file, "r+")) === false) {
        return false;
    }
    fclose($fp);
    return true;
}

$errorInfo = null;
if (is_file(INSTALL_PATH . DS . 'install.lock')) {
    $errorInfo = '已安装系统，如需重新安装请删除文件：/public/install/install.lock';
} elseif (!isReadWrite(ROOT_PATH . 'config' . DS)) {
    $errorInfo = ROOT_PATH . 'config' . DS . '：读写权限不足';
} elseif (!isReadWrite(ROOT_PATH . 'runtime' . DS)) {
    $errorInfo = ROOT_PATH . 'runtime' . DS . '：读写权限不足';
} elseif (!isReadWrite(ROOT_PATH . 'public' . DS)) {
    $errorInfo = ROOT_PATH . 'public' . DS . '：读写权限不足';
} elseif (!checkPhpVersion('7.2.0')) {
    $errorInfo = 'PHP版本不能小于7.2.0';
} elseif (!extension_loaded("PDO")) {
    $errorInfo = '当前未开启PDO，无法进行安装';
}

// POST请求
if (isAjax()) {
    $post = $_POST;

    $cover = $post['cover'] == 1 ? true : false;
    $cache = $post['cache'] == 1 ? 'redis' : 'file';
    $cachePassword = !empty($post['cache_password']) ? $post['cache_password'] : ''; 
    $database = $post['database'];
    $hostname = $post['hostname'];
    $hostport = $post['hostport'];
    $dbUsername = $post['db_username'];
    $dbPassword = $post['db_password'];
    $prefix = $post['prefix'];
    $adminUrl = $post['admin_url'];
    $username = $post['username'];
    $password = $post['password'];
    $authcode = $post['authcode'];

    // 参数验证
    $validateError = null;

    // 判断是否有特殊字符
    $check = preg_match('/[0-9a-zA-Z]+$/', $adminUrl, $matches);
    if (!$check) {
        $validateError = '后台地址不能含有特殊字符, 只能包含字母或数字。';
        $data = [
            'code' => 0,
            'msg'  => $validateError,
        ];
        die(json_encode($data));
    }
    
    // 查询授权
    $zz_auth_error = '';
    $zz_id = '2';
    $zz_url = 'auth.zzwws.cn';
    $root = $_SERVER['DOCUMENT_ROOT'];
    $host = $_SERVER['HTTP_HOST'];
    $zz_query=get_curl('http://'.$zz_url.'/ZR/api/zr_check.php?url='.$host.'&authcode='.$authcode.'&version=&ZRid='.$zz_id);
    if ($zz_query = json_decode($zz_query, true)) {
		if ($zz_query["code"] == 0) {
		}else{
			$zz_auth_error = $zz_query['msg'];
		}
	}else{
	    $zz_auth_error = '查询授权信息失败';
	}
    
    if (strlen($adminUrl) < 2) {
        $validateError = '后台的地址不能小于2位数';
    } elseif (strlen($password) < 5) {
        $validateError = '管理员密码不能小于5位数';
    } elseif (strlen($username) < 4) {
        $validateError = '管理员账号不能小于4位数';
    } elseif (!$authcode) {
        $validateError = '请填写授权码';
    } elseif ($zz_auth_error){
        $validateError = $zz_auth_error;
    }
    if (!empty($validateError)) {
        $data = [
            'code' => 0,
            'msg'  => $validateError,
        ];
        die(json_encode($data));
    }

    // 错误提示
    if($errorInfo){
        $data = [
            'code' => 0,
            'msg'  => $errorInfo,
        ];
        die(json_encode($data));
    }

    // DB类初始化
    $config = [
        'type'     => 'mysql',
        'hostname' => $hostname,
        'username' => $dbUsername,
        'password' => $dbPassword,
        'hostport' => $hostport,
        'charset'  => 'utf8',
        'prefix'   => $prefix,
        'debug'    => true,
    ];
    Db::setConfig([
        'default'     => 'mysql',
        'connections' => [
            'mysql'   => $config,
            'install' => array_merge($config, ['database' => $database]),
        ],
    ]);

    // 检测数据库连接
    if (!checkConnect()) {
        $data = [
            'code' => 0,
            'msg'  => '数据库连接失败',
        ];
        die(json_encode($data));
    }

    // 检测数据库是否存在
    // if (!$cover && checkDatabase($database)) {
    //     $data = [
    //         'code' => 0,
    //         'msg'  => '数据库已存在，请选择覆盖安装或者修改数据库名',
    //     ];
    //     die(json_encode($data));
    // }
    if(!checkDatabase($database)){
        $data = [
            'code' => 0,
            'msg'  => '数据库不存在，请检查是否有该数据库',
        ];
        die(json_encode($data));
    }

    // 覆盖数据库
    if($cover){
        $sql = '';
        $tables = Db::query("SHOW TABLES FROM `{$database}`");
        foreach($tables as $v){
            if(stripos(array_values($v)[0],$prefix) !== false){
                $sql .= "`".array_values($v)[0]."`,";
            }
        }
        $sql = rtrim($sql,',');
        if($sql){
            $sql = 'DROP TABLE '.$sql;
            Db::connect('install')->query($sql);
        }
    }

    // 选择redis时检测是否已安装redis
    if($cache == 'redis' && !extension_loaded('redis')){
        $data = [
            'code' => 0,
            'msg'  => '未安装redis，请先安装redis或者选择file',
        ];
        die(json_encode($data));
    }
    
    // 创建数据库
    // createDatabase($database);
    // 导入sql语句等等
    $install = install($username, $password, array_merge($config, ['database' => $database,'adminUrl' => $adminUrl,'cache' => $cache,'cachePassword' => $cachePassword,'authcode' => $authcode]));
    if ($install !== true) {
        $data = [
            'code' => 0,
            'msg'  => '系统安装失败：' . $install,
        ];
        die(json_encode($data));
    }
    // 清空缓存文件
    do_rmdir(ROOT_PATH.'runtime',false);
    do_rmdir(ROOT_PATH.'public'.DS.'ck',false);
    $data = [
        'code' => 1,
        'msg'  => '系统安装成功，正在跳转登录页面',
        'url'  => $adminUrl,
    ];
    die(json_encode($data));
}


function isAjax()
{
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        return true;
    } else {
        return false;
    }
}

function isPost()
{
    return ($_SERVER['REQUEST_METHOD'] == 'POST' && checkurlHash($GLOBALS['verify'])
        && (empty($_SERVER['HTTP_REFERER']) || preg_replace("~https?:\/\/([^\:\/]+).*~i", "\\1", $_SERVER['HTTP_REFERER']) == preg_replace("~([^\:]+).*~", "\\1", $_SERVER['HTTP_HOST']))) ? 1 : 0;
}

function checkPhpVersion($version)
{
    $php_version = explode('-', phpversion());
    $check = strnatcasecmp($php_version[0], $version) >= 0 ? true : false;
    return $check;
}

function checkConnect()
{
    try {
        Db::query("select version()");
    } catch (\Exception $e) {
        return false;
    }
    return true;
}

function checkDatabase($database)
{
    $check = Db::query("SELECT * FROM information_schema.schemata WHERE schema_name='{$database}'");
    if (empty($check)) {
        return false;
    } else {
        return true;
    }
}

function createDatabase($database)
{
    try {
        Db::execute("CREATE DATABASE IF NOT EXISTS `{$database}` DEFAULT CHARACTER SET utf8");
    } catch (\Exception $e) {
        return false;
    }
    return true;
}

function parseSql($sql = '', $to, $from)
{
    list($pure_sql, $comment) = [[], false];
    $sql = explode("\n", trim(str_replace(["\r\n", "\r"], "\n", $sql)));
    foreach ($sql as $key => $line) {
        if ($line == '') {
            continue;
        }
        if (preg_match("/^(#|--)/", $line)) {
            continue;
        }
        if (preg_match("/^\/\*(.*?)\*\//", $line)) {
            continue;
        }
        if (substr($line, 0, 2) == '/*') {
            $comment = true;
            continue;
        }
        if (substr($line, -2) == '*/') {
            $comment = false;
            continue;
        }
        if ($comment) {
            continue;
        }
        if ($from != '') {
            $line = str_replace('`' . $from, '`' . $to, $line);
        }
        if ($line == 'BEGIN;' || $line == 'COMMIT;') {
            continue;
        }
        array_push($pure_sql, $line);
    }
    //$pure_sql = implode($pure_sql, "\n");
    $pure_sql = implode("\n",$pure_sql);
    $pure_sql = explode(";\n", $pure_sql);
    return $pure_sql;
}

function install($username, $password, $config)
{
    $sqlPath = file_get_contents(ROOT_PATH . 'install.sql');
    $sqlArray = parseSql($sqlPath, $config['prefix'], 'zz_');
    Db::startTrans();
    try {
        foreach ($sqlArray as $vo) {
            Db::connect('install')->execute($vo);
        }
        $res = Db::connect('install')
            ->name('system_admin')
            ->where('id',1)
            ->update([
                'username'    => $username,
                'password'    => password_hash($password,PASSWORD_BCRYPT),
                'create_time' => time(),
                'update_time' => 0,
                'login_time'  => 0,
                'head_img'    => '/static/admin/images/head.jpg',
                'status'      => 1,
                'phone'       => '',
                'email'       => '',
                'role'        => 1
            ]);
        if(!$res){
            return '修改管理员信息失败';
        }
        $http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
        $url = $http_type.$_SERVER['HTTP_HOST'].'/';
        Db::connect('install')->name('system_log')->delete(true);
        Db::connect('install')->name('system_notice')->delete(true);
        Db::connect('install')->name('system_config')->where('k','=','url')->update(['v' => $url]);
        Db::connect('install')->name('system_config')->where('k','=','epayurl_ali')->update(['v' => $url]);
        Db::connect('install')->name('system_config')->where('k','=','epayid_ali')->update(['v' => mt_rand(1000000000,9999999999)]);
        Db::connect('install')->name('system_config')->where('k','=','epaykey_ali')->update(['v' => md5(date('YmdHis').mt_rand(1000000000,9999999999))]);
        Db::connect('install')->name('system_config')->where('k','=','cron_key')->update(['v' => md5(date('YmdHis').mt_rand(1000000000,9999999999))]);
        Db::connect('install')->name('system_config')->where('k','=','authcode')->update(['v' => $config['authcode']]);

        // 处理安装文件
        !is_dir(INSTALL_PATH) && @mkdir(INSTALL_PATH);
        !is_dir(INSTALL_PATH . DS) && @mkdir(INSTALL_PATH . DS);
        @file_put_contents(INSTALL_PATH . DS . 'install.lock', date('Y-m-d H:i:s'));
        @file_put_contents(ROOT_PATH . '.env', getDatabaseConfig($config));
        Db::commit();
    } catch (\Exception $e) {
        Db::rollback();
        return $e->getMessage();
    }
    return true;
}

function getDatabaseConfig($data)
{
    $config = <<<EOT
APP_DEBUG = false

[APP]
DEFAULT_TIMEZONE = Asia/Shanghai

[DATABASE]
TYPE = mysql
HOSTNAME = {$data['hostname']}
DATABASE = {$data['database']}
USERNAME = {$data['username']}
PASSWORD = {$data['password']}
HOSTPORT = {$data['hostport']}
CHARSET = utf8
DEBUG = true
PREFIX = {$data['prefix']}

[ZZ]
# 后台登录验证码开关
CAPTCHA=true

# 后台管理员地址后缀名称
ADMIN = {$data['adminUrl']}

# 静态文件路径前缀
STATIC_PATH = /static/admin

[CACHE]
# 默认缓存驱动
DRIVER = {$data['cache']}
# 缓存驱动密码
PASSWORD = {$data['cachePassword']}

[LANG]
default_lang = zh-cn

EOT;
    return $config;
}

function do_rmdir($dirname, $self = true)
{
    if (!file_exists($dirname)) {
        return false;
    }
    if (is_file($dirname) || is_link($dirname)) {
        return unlink($dirname);
    }
    $dir = dir($dirname);
    if ($dir) {
        while (false !== $entry = $dir->read()) {
            if ($entry == '.' || $entry == '..') {
                continue;
            }
            do_rmdir($dirname . '/' . $entry);
        }
    }
    $dir->close();
    $self && rmdir($dirname);
}

function get_curl($url, $post = '', $cookie = '', $referer = '', $proxy = '', $header = 0, $userAgent = '', $httpheader = [], $timeout = 10)
{
    $curl = curl_init();
    // 配置curl中的http协议->可配置的荐可以查PHP手册中的curl_  
    curl_setopt($curl, CURLOPT_URL, $url);
    if ($post) {
        // POST数据
        curl_setopt($curl, CURLOPT_POST, 1);
        // 把post的变量加上
        if (is_array($post)) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($post));
        } else {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $post);
        }
        if (is_string($post) && $arr = json_decode($post, true)) {
            if (is_array($arr)) {
                $httpheader[] = 'Content-Type: application/json; charset=utf-8';
                $httpheader[] = 'Content-Length: ' . strlen($post);
            }
        }
    }
    if ($referer) {
        $httpheader[] = 'Referer: ' . $referer; //模拟来路
        $httpheader[] = 'Origin: ' . $referer;
    } else {
        $httpheader[] = 'Referer: ' . $url; //模拟来路
        $httpheader[] = 'Origin: ' . $url;
    }
    if ($cookie) {
        $httpheader[] = 'Cookie: ' . $cookie; //模拟cookie
    }
    if ($proxy) {
        $proxy = explode(':', $proxy);
        if (!empty($proxy[1])) {
            curl_setopt($curl, CURLOPT_PROXY, $proxy[0]); //代理服务器地址
            curl_setopt($curl, CURLOPT_PROXYPORT, $proxy[1]); //代理服务器端口
        }
        $httpheader[] = 'X-FORWARDED-FOR: ' . $proxy[0]; //模拟ip
        $httpheader[] = 'CLIENT-IP: ' . $proxy[0]; //模拟ip
    } else {
        $httpheader[] = 'X-FORWARDED-FOR: ' . $_SERVER['REMOTE_ADDR']; //模拟ip
        $httpheader[] = 'CLIENT-IP: ' . $_SERVER['REMOTE_ADDR']; //模拟ip
    }
    if ($header) {
        curl_setopt($curl, CURLOPT_HEADER, TRUE); //获取响应头信息
    }
    if ($userAgent) {
        $httpheader[] = 'User-Agent: ' . $userAgent; //模拟用户浏览器信息 
    } else {
        $httpheader[] = 'User-Agent: ' . (!empty($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36');
    }
    $parseUrl = parse_url($url);
    if (!empty($parseUrl['host'])) {
        $httpheader[] = 'Host: ' . $parseUrl['host'];
    }
    curl_setopt($curl, CURLOPT_HTTPHEADER, $httpheader); //模拟请求头
    curl_setopt($curl, CURLOPT_TIMEOUT, $timeout); //只需要设置一个秒的数量就可以  
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1); //返回字符串，而非直接输出到屏幕上
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1); //跟踪爬取重定向页面
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($curl, CURLOPT_ENCODING, ''); //解决网页乱码问题
    // 执行这个请求  
    $ret = curl_exec($curl);
    if ($header) {
        $headerSize = curl_getinfo($curl, CURLINFO_HEADER_SIZE);
        $header = substr($ret, 0, $headerSize);
        $body = substr($ret, $headerSize);
        $ret = array();
        $ret['header'] = $header;
        $ret['body'] = $body;
    }
    curl_close($curl);
    return $ret;
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>安装<?php echo $title;?>程序</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="icon" href="/static/admin/images/favicon.ico">
    <link rel="stylesheet" href="/static/admin/lib/layui-v2.11.4/css/layui.css?v=<?php echo time() ?>" media="all">
    <link rel="stylesheet" href="/static/common/css/install.css?v=<?php echo time() ?>" media="all">
</head>
<body>
<h1><img src="/static/admin/images/logo.png"></h1>
<h2>安装<?php echo $title;?></h2>
<div class="content">
    <!-- <p class="desc">
        使用过程中遇到任何问题可参考
        <a href="http://easyadmin.99php.cn/docs" target="_blank">文档教程</a>
        <a href="https://jq.qq.com/?_wv=1027&k=5IHJawE">QQ交流群</a>
    </p> -->
    <form class="layui-form layui-form-pane" action="">
        <?php if ($errorInfo): ?>
            <div class="error">
                <?php echo $errorInfo; ?>
            </div>
        <?php endif; ?>
        <div class="bg">
            <div class="layui-form-item">
                <label class="layui-form-label">数据库地址</label>
                <div class="layui-input-block">
                    <input class="layui-input" name="hostname" autocomplete="off" lay-verify="required" lay-reqtext="请输入数据库地址" placeholder="请输入数据库地址" value="127.0.0.1">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">数据库端口</label>
                <div class="layui-input-block">
                    <input class="layui-input" name="hostport" autocomplete="off" lay-verify="required" lay-reqtext="请输入数据库端口" placeholder="请输入数据库端口" value="3306">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">数据库名称</label>
                <div class="layui-input-block">
                    <input class="layui-input" name="database" autocomplete="off" lay-verify="required" lay-reqtext="请输入数据库名称" placeholder="请输入数据库名称" value="zz">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">数据库账号</label>
                <div class="layui-input-block">
                    <input class="layui-input" name="db_username" autocomplete="off" lay-verify="required" lay-reqtext="请输入数据库账号" placeholder="请输入数据库账号" value="root">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">数据库密码</label>
                <div class="layui-input-block">
                    <input type="password" class="layui-input" name="db_password" autocomplete="off" lay-verify="required" lay-reqtext="请输入数据库密码" placeholder="请输入数据库密码">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">数据表前缀</label>
                <div class="layui-input-block">
                    <input class="layui-input" name="prefix" autocomplete="off" lay-verify="required" lay-reqtext="请输入数据表前缀" placeholder="请输入数据表前缀" value="zz_">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">覆盖数据库</label>
                <div class="layui-input-block" style="text-align: left">
                    <input type="radio" name="cover" value="1" title="覆盖">
                    <input type="radio" name="cover" value="0" title="不覆盖" checked>
                    <span class="tips">覆盖会删除已有的数据表，请注意！</span>
                </div>
            </div>
        </div>
        <div class="bg">
            <div class="layui-form-item">
                <label class="layui-form-label">缓存驱动</label>
                <div class="layui-input-block" style="text-align: left">
                    <input type="radio" name="cache" value="0" title="file" lay-filter="cache">
                    <input type="radio" name="cache" value="1" title="redis" checked lay-filter="cache">
                    <span class="tips">缓存驱动配置信息在config/cache.php</span>
                </div>
            </div>

            <div class="layui-form-item" id="cache_password" style="display: none;">
                <label class="layui-form-label">redis密码</label>
                <div class="layui-input-block">
                <input type="password" class="layui-input" name="cache_password" autocomplete="off" placeholder="请输入redis密码">
                    <span class="tips">首次设置密码后，需要重启redis才会生效</span>
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">后台的地址</label>
                <div class="layui-input-block">
                    <input class="layui-input" id="admin_url" name="admin_url" autocomplete="off" lay-verify="required" lay-reqtext="请输入后台的地址" placeholder="为了后台安全，不建议将后台路径设置为admin" value="admin">
                    <span class="tips">后台登录地址： <?php echo $currentHost; ?><span id="admin_name">admin</span></span>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">管理员账号</label>
                <div class="layui-input-block">
                    <input class="layui-input" name="username" autocomplete="off" lay-verify="required" lay-reqtext="请输入管理员账号" placeholder="请输入管理员账号" value="admin">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">管理员密码</label>
                <div class="layui-input-block">
                    <input type="password" class="layui-input" name="password" autocomplete="off" lay-verify="required" lay-reqtext="请输入管理员密码" placeholder="请输入管理员密码">
                </div>
            </div>
            
            <div class="layui-form-item">
                <label class="layui-form-label">授权码</label>
                <div class="layui-input-block">
                    <input class="layui-input" name="authcode" autocomplete="off" lay-verify="required" lay-reqtext="请输入授权码" placeholder="请输入授权码" value="">
                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <button class="layui-btn layui-btn-normal <?php echo $errorInfo ? 'layui-btn-disabled' : '' ?>" lay-submit="" lay-filter="install">确定安装</button>
        </div>
    </form>
</div>
<script src="/static/admin/lib/layui-v2.11.4/layui.js?v=<?php echo time() ?>" charset="utf-8"></script>
<script>
    layui.use(['form', 'layer'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer;

        $("#admin_url").bind("input propertychange", function () {
            var val = $(this).val();
            $("#admin_name").text(val);
        });

        form.on('submit(install)', function (data) {
            if ($(this).hasClass('layui-btn-disabled')) {
                return false;
            }
            var _data = data.field;
            var loading = layer.msg('正在安装...', {
                icon: 16,
                shade: 0.2,
                time: false
            });
            $.ajax({
                url: window.location.href,
                type: 'post',
                contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                dataType: "json",
                data: _data,
                timeout: 60000,
                success: function (data) {
                    layer.close(loading);
                    if (data.code === 1) {
                        layer.msg(data.msg, {icon: 1}, function () {
                            window.location.href = location.protocol + '//' + location.host + '/' + data.url;
                        });
                    } else {
                        layer.msg(data.msg, {icon: 2});
                    }
                },
                error: function (xhr, textstatus, thrown) {
                    layer.close(loading);
                    layer.msg('Status:' + xhr.status + '，' + xhr.statusText + '，请稍后再试！', {icon: 2});
                    return false;
                }
            });
            return false;
        });

        form.on('radio(cache)',function(data){
            // console.log(data)
            if(data.value == 1){
                $('#cache_password').show();
            }else{
                $('#cache_password').hide();
            }
        })
    });
</script>
</body>
</html>
