<?php

namespace app\admin\controller\system;

use app\admin\model\SystemAdmin;
use app\common\controller\CommonController;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;

/**
 * @ControllerAnnotation(title="日志管理")
 */
class Log extends CommonController
{
    public function __construct()
    {
        $authcode = get_setting('authcode');
        if(!$authcode){
            $this->error('请填写授权码');
        }
        $zz_id = '2';
        $zz_url = 'auth.zzwws.cn';
        $root = $_SERVER['DOCUMENT_ROOT'];
        $host = $_SERVER['HTTP_HOST'];
        $server_ip = !empty($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : $_SERVER['APP_POOL_ID'];
        $date = date("Y-m-d");
        $zz_auth = cache('zz_auth_'.$server_ip);
        if(md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM') !== $zz_auth){
        $zz_query=get_curl('http://'.$zz_url.'/ZR/api/zr_check.php?url='.$host.'&authcode='.$authcode.'&version=&ZRid='.$zz_id);
            if ($zz_query = json_decode($zz_query, true)) {
        		if ($zz_query["code"] == 0) {
        			cache('zz_auth_'.$server_ip,md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM'),3600*24);
        		}else{
        			$this->error($zz_query['msg']);
        		}
        	}else{
        	    $this->error('查询授权信息失败');
        	}
        }
        
        $this->model = new \app\admin\model\SystemLog();
    }

    /**
	* @NodeAnotation(title="列表")
	*/
	public function index()
	{
		if(request()->isAjax()){
			$data = $this->model->index();
			return json([
				'code' => 0,
				'msg' => '',
				'count' => $data['count'],
				'data' => $data['data']
			]);
		}
		$arr = [['id' => 0,'username' => 'system'],['id' => -1,'username' => 'tourist']];
		$admin = SystemAdmin::field('id,username')->select()->toArray();
		$admin = array_merge($arr,$admin);
		return view('',['admin' => $admin]);
	}

    /**
	* @NodeAnotation(title="删除")
	*/
	public function del()
	{
		if(request()->isAjax()){
			return $this->model->del();
		}
	}
}