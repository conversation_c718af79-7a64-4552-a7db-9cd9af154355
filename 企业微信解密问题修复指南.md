# 企业微信消息解密失败修复指南

## 问题描述
企业微信消息解密失败，错误码: -40001（签名验证错误）

## 错误分析
错误码 -40001 表示签名验证错误，这是企业微信消息解密中最常见的问题。主要原因包括：

1. **配置参数错误**：Token、EncodingAESKey、CorpId配置不正确
2. **签名算法问题**：签名生成方式与企业微信要求不符
3. **参数格式问题**：配置参数包含多余空格或格式错误
4. **应用配置不匹配**：使用了错误的应用配置信息

## 修复步骤

### 1. 检查企业微信管理后台配置

#### 1.1 登录企业微信管理后台
- 访问：https://work.weixin.qq.com/
- 使用管理员账号登录

#### 1.2 获取正确的配置信息
1. 进入 **应用管理** -> 选择对应的应用
2. 在 **接收消息** 设置中查看：
   - **Token**：自定义的令牌
   - **EncodingAESKey**：43位的加密密钥
   - **企业ID (CorpID)**：以ww开头的企业标识

#### 1.3 验证配置格式
- **Token**：长度至少3位，建议使用字母数字组合
- **EncodingAESKey**：必须是43位字符，通常由企业微信自动生成
- **CorpID**：格式为 `ww` + 14-16位字母数字，如：`ww666a49fdd2b8622e`

### 2. 检查代码中的配置

#### 2.1 查看当前配置
检查 `app/common/service/WechatWorkService.php` 中的配置获取：

```php
$this->token = get_setting('wechat_work_token');
$this->encodingAESKey = get_setting('wechat_work_encoding_aes_key');
$this->corpId = get_setting('wechat_work_corp_id');
```

#### 2.2 验证配置存储
检查数据库中的配置是否正确：

```sql
SELECT * FROM system_config WHERE k IN (
    'wechat_work_token',
    'wechat_work_encoding_aes_key', 
    'wechat_work_corp_id'
);
```

#### 2.3 常见配置问题
- 配置值包含前后空格
- EncodingAESKey长度不是43位
- CorpID格式错误（不是以ww开头）
- 使用了测试环境的配置

### 3. 使用诊断工具

#### 3.1 运行快速测试脚本
```bash
php test_decrypt_quick.php
```

#### 3.2 使用Web诊断工具
访问：`http://your-domain.com/wechat_work_decrypt_diagnose.php`

#### 3.3 查看详细日志
检查日志文件：`runtime/log/当前日期.log`

### 4. 手动验证签名算法

#### 4.1 签名生成规则
企业微信的签名生成步骤：
1. 将 token、timestamp、nonce、encrypt 四个参数进行字典序排序
2. 将排序后的参数拼接成一个字符串
3. 对拼接后的字符串进行 SHA1 加密
4. 得到的结果与 msg_signature 进行比较

#### 4.2 PHP实现示例
```php
$params = [$token, $timestamp, $nonce, $encrypt];
sort($params, SORT_STRING);
$sortedStr = implode($params);
$expectedSignature = sha1($sortedStr);
```

### 5. 常见问题排查

#### 5.1 配置问题
- [ ] Token是否包含特殊字符或空格
- [ ] EncodingAESKey是否完整（43位）
- [ ] CorpID是否正确（ww开头）
- [ ] 是否使用了正确的应用配置

#### 5.2 环境问题
- [ ] PHP版本是否支持所需的加密函数
- [ ] 企业微信加密库文件是否完整
- [ ] 服务器时间是否正确

#### 5.3 网络问题
- [ ] 回调URL是否可以正常访问
- [ ] SSL证书是否有效（HTTPS）
- [ ] 服务器IP是否在企业微信白名单中

### 6. 修复代码改进

#### 6.1 增强的错误处理
已在 `WechatWorkService.php` 中添加了详细的错误分析：

```php
private function analyzeDecryptError(int $errCode, string $msgSignature, string $timestamp, string $nonce, string $encrypt): void
{
    switch ($errCode) {
        case -40001:
            Log::error('签名验证错误，尝试分析原因:');
            // 详细的签名分析逻辑
            break;
        // 其他错误码处理...
    }
}
```

#### 6.2 配置验证
添加了配置格式验证：

```php
// 验证配置格式
if (strlen($this->encodingAESKey) !== 43) {
    Log::error('企业微信EncodingAESKey长度错误，应为43位，当前为' . strlen($this->encodingAESKey) . '位');
    return [];
}

if (!preg_match('/^ww[a-zA-Z0-9]{14,16}$/', $this->corpId)) {
    Log::error('企业微信CorpId格式错误，应以ww开头，当前为: ' . $this->corpId);
    return [];
}
```

### 7. 测试验证

#### 7.1 配置测试
1. 使用诊断工具验证配置格式
2. 检查企业微信管理后台设置
3. 确认配置信息完全一致

#### 7.2 解密测试
1. 使用实际的消息数据进行测试
2. 对比期望签名和实际签名
3. 验证解密结果是否正确

#### 7.3 集成测试
1. 在企业微信中发送测试消息
2. 检查日志中的详细信息
3. 确认消息能够正常解密和处理

## 预防措施

### 1. 配置管理
- 使用配置文件或环境变量管理敏感信息
- 定期备份配置信息
- 避免在代码中硬编码配置

### 2. 监控告警
- 设置解密失败的监控告警
- 定期检查日志文件
- 监控企业微信API调用状态

### 3. 文档维护
- 记录配置变更历史
- 维护详细的部署文档
- 建立故障处理流程

## 联系支持

如果按照以上步骤仍无法解决问题，请：

1. 收集详细的错误日志
2. 记录配置信息（脱敏处理）
3. 提供测试用的消息数据
4. 联系技术支持团队

## 相关文件

- `app/common/service/WechatWorkService.php` - 企业微信服务类
- `app/index/controller/WechatWork.php` - 企业微信控制器
- `extend/callback/` - 企业微信加密库
- `test_decrypt_quick.php` - 快速测试脚本
- `wechat_work_decrypt_diagnose.php` - 诊断工具

## 更新日志

- 2025-08-21：添加详细的错误分析和诊断工具
- 2025-08-21：增强配置验证和错误处理
- 2025-08-21：创建修复指南文档
