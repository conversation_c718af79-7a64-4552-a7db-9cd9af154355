<?php
namespace app\admin\controller\system;

use app\common\controller\CommonController;
use app\admin\model\SystemAdmin;
use app\admin\model\SystemRole;
use EasyAdmin\annotation\ControllerAnnotation;
use EasyAdmin\annotation\NodeAnotation;

/**
 * @ControllerAnnotation(title="管理员管理")
 */
class Admin extends CommonController
{
	public function __construct()
	{
	    $authcode = get_setting('authcode');
        if(!$authcode){
            $this->error('请填写授权码');
        }
        $zz_id = '2';
        $zz_url = 'auth.zzwws.cn';
        $root = $_SERVER['DOCUMENT_ROOT'];
        $host = $_SERVER['HTTP_HOST'];
        $server_ip = !empty($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : $_SERVER['APP_POOL_ID'];
        $date = date("Y-m-d");
        $zz_auth = cache('zz_auth_'.$server_ip);
        if(md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM') !== $zz_auth){
        $zz_query=get_curl('http://'.$zz_url.'/ZR/api/zr_check.php?url='.$host.'&authcode='.$authcode.'&version=&ZRid='.$zz_id);
            if ($zz_query = json_decode($zz_query, true)) {
        		if ($zz_query["code"] == 0) {
        			cache('zz_auth_'.$server_ip,md5($host.'|'.$root.'|'.$server_ip.'|'.$authcode.'|'.$date.'|'.'UpAFRQhLKuAiM3FqVmdLi3QFOIvrZimM'),3600*24);
        		}else{
        			$this->error($zz_query['msg']);
        		}
        	}else{
        	    $this->error('查询授权信息失败');
        	}
        }
        
		$this->model = new SystemAdmin();
	}

	/**
	* @NodeAnotation(title="列表")
	*/
	public function index()
	{
		if(request()->isAjax()){
			$data = $this->model->index();
			return json([
				'code'=>0,
				'msg'=>'',
				'count'=>$data['count'],
				'data'=>$data['data']
			]);
		}
		$role = SystemRole::field('id,name')->select();
		return view('',['role' => $role]);
	}

	/**
	* @NodeAnotation(title="添加")
	*/
	public function add()
	{
		if(request()->isAjax()){
			return $this->model->add();
		}
		$role = SystemRole::field('id,name')->select();
		return view('',['role' => $role,'head_img' => $this->model->headImg]);
	}

	/**
	* @NodeAnotation(title="编辑")
	*/
	public function edit()
	{
		if(request()->isAjax()){
			return $this->model->edit();
		}

		$admin = $this->model->withoutField('password,create_time,login_time')->where('id',input('id'))->find();
		if(!$admin){
			$this->error('没有找到该用户');
		}
		$data['admin'] = $admin;
		$data['role'] = SystemRole::field('id,name')->select();
		return view('',$data);
	}

	/**
	* @NodeAnotation(title="状态")
	*/
	public function status()
	{
		if(request()->isAjax()){
			return $this->model->status();
		}
	}

	/**
	* @NodeAnotation(title="删除")
	*/
	public function del()
	{
		if(request()->isAjax()){
			return $this->model->del();
		}
	}

	/**
	 * @NodeAnotation(title="基本资料")
	 */
	public function info()
	{
		if(request()->isAjax()){
			return $this->model->info();
		}
		$row = $this->model->field('username,head_img,phone,email')->where('id',USER_INFO['id'])->find();
		if(!$row){
			$this->error('没有找到该用户');
		}
		return view('',['row' => $row]);
	}

	/**
	 * @NodeAnotation(title="修改密码")
	 */
	public function password()
	{
		if(request()->isAjax()){
			return $this->model->password();
		}
		$row = $this->model->field('username')->where('id',USER_INFO['id'])->find();
		if(!$row){
			$this->error('没有找到该用户');
		}
		return view('',['row' => $row]);
	}
}
